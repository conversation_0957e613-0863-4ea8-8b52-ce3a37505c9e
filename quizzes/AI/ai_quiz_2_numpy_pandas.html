<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Quiz 2: NumPy & Pandas Data Cleaning | Python Data Science</title>
  <meta name="description" content="AI Quiz 2: Free NumPy and Pandas data cleaning quiz with 15 questions covering data preprocessing, missing values, and vectorized operations. Perfect for Python data science learners.">
  <meta name="keywords" content="AI quiz, Python quiz, NumPy, Pandas, data cleaning, data science, missing values, vectorized operations, DataFrame, Python tutorial">
  
  <!-- Add structured data for rich results -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Quiz",
    "name": "AI Quiz 2: NumPy & Pandas Data Cleaning Quiz",
    "description": "AI Quiz 2: Free NumPy and Pandas data cleaning quiz with 15 questions covering data preprocessing, missing values, and vectorized operations. Perfect for Python data science learners.",
    "educationalAlignment": {
      "@type": "AlignmentObject",
      "alignmentType": "educationalSubject",
      "targetName": "Computer Programming"
    },
    "educationalUse": "Practice Quiz",
    "audience": {
      "@type": "EducationalAudience",
      "educationalRole": "student"
    },
    "provider": {
      "@type": "Person",
      "name": "Faruk Hasan",
      "url": "https://faruk-hasan.com"
    },
    "about": [
      "Python",
      "NumPy",
      "Pandas",
      "Data Cleaning",
      "Data Science"
    ]
  }
  </script>
  
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Shared Header/Footer CSS -->
  <link rel="stylesheet" href="../../shared/header-footer.css">
  
  <style>
    :root {
      --primary-color: #3776ab;
      --primary-dark: #2b5b84;
      --secondary-color: #f5f7fa;
      --text-color: #333;
      --light-gray: #f5f5f5;
      --border-color: #e0e0e0;
      --success-color: #27ae60;
      --error-color: #e74c3c;
      --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    body {
      /* Body padding/margin removed to match resources.html header layout */
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      
      
      background-color: var(--secondary-color);
      color: var(--text-color);
    }
    
    h1 {
      text-align: center;
      color: var(--primary-color);
      margin-bottom: 30px;
      font-size: 2.2rem;
      border-bottom: 2px solid var(--border-color);
      padding-bottom: 10px;
    }
    
    .quiz-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 10px;
      box-shadow: var(--box-shadow);
      padding: 25px;
    }
    
    .question {
      margin-bottom: 25px;
      padding: 20px;
      background: #eef2f5;
      border-left: 4px solid var(--primary-color);
      border-radius: 5px;
      transition: transform 0.2s ease, box-shadow 0.3s ease;
    }
    
    .question:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    }
    
    .question p {
      font-weight: bold;
      margin-top: 0;
      font-size: 1.1rem;
    }
    
    .options {
      margin-top: 15px;
    }
    
    .options label {
      display: block;
      padding: 10px 15px;
      margin-bottom: 8px;
      background: white;
      border-radius: 4px;
      border: 1px solid var(--border-color);
      cursor: pointer;
      transition: background-color 0.2s, border-color 0.2s;
    }
    
    .options label:hover {
      background-color: #f0f7ff;
      border-color: var(--primary-color);
    }
    
    input[type="radio"] {
      margin-right: 10px;
    }
    
    #submitBtn {
      display: block;
      width: 100%;
      padding: 15px;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 5px;
      font-size: 1.1rem;
      cursor: pointer;
      transition: background-color 0.3s;
      margin-top: 20px;
    }
    
    #submitBtn:hover {
      background-color: var(--primary-dark);
    }
    
    #result {
      margin-top: 20px;
      padding: 15px;
      text-align: center;
      font-size: 1.2rem;
      border-radius: 5px;
      display: none;
    }
    
    /* Progress bar styles */
    .progress-wrapper {
      position: sticky;
      top: 0;
      z-index: 100;
      background: white;
      padding: 10px 0;
      border-bottom: 1px solid var(--border-color);
      margin-bottom: 20px;
    }
    
    .progress-container {
      height: 10px;
      background-color: #e0e0e0;
      border-radius: 5px;
      overflow: hidden;
    }
    
    #progress-fill {
      height: 100%;
      background-color: var(--primary-color);
      width: 0%;
      transition: width 0.3s ease;
    }
    
    #progress-count {
      text-align: center;
      font-size: 0.9rem;
      margin-top: 5px;
      color: #666;
    }
    
    /* Results styles */
    #results {
      display: none;
      margin-top: 30px;
    }
    
    .score-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .score-circle {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      background: var(--primary-color);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2.5rem;
      font-weight: bold;
      margin-bottom: 10px;
    }
    
    .score-label {
      font-size: 1.1rem;
      color: #666;
    }
    
    .feedback {
      text-align: center;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
      font-size: 1.1rem;
    }
    
    .answer-key {
      margin-top: 30px;
      border-top: 1px solid var(--border-color);
      padding-top: 20px;
    }
    
    .answer-item {
      margin-bottom: 15px;
      padding: 10px;
      border-radius: 5px;
    }
    
    .answer-item.correct {
      background-color: rgba(39, 174, 96, 0.1);
      border-left: 3px solid var(--success-color);
    }
    
    .answer-item.incorrect {
      background-color: rgba(231, 76, 60, 0.1);
      border-left: 3px solid var(--error-color);
    }
    
    .answer-item h4 {
      margin-top: 0;
      margin-bottom: 10px;
    }
    
    .user-answer {
      margin-bottom: 5px;
    }
    
    .correct-answer {
      font-weight: bold;
    }
    

    
    /* Intro section styles */
    .intro-section {
      margin-bottom: 25px;
    }
    
    .intro-section a {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: bold;
      display: inline-block;
      margin-top: 5px;
      transition: color 0.2s;
    }
    
    .intro-section a:hover {
      color: var(--primary-dark);
      text-decoration: underline;
    }
    
    .intro-section a i {
      margin-right: 5px;
    }

    
    /* Code block styles */
    .code-block {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 10px;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
      margin: 10px 0;
      overflow-x: auto;
    }
  </style>
</head>
<body>

  <h1>AI Quiz 2: NumPy & Pandas Data Cleaning</h1>




  <div class="quiz-container">
    <!-- Add an introduction paragraph for SEO -->
    <div class="intro-section">
      <h2 style="text-align:center;">AI Quiz 2: 15 NumPy & Pandas Data Cleaning Questions</h2>
      <p>This quiz tests your knowledge of data cleaning with NumPy and Pandas, focusing on handling missing values, vectorized operations, and DataFrame manipulation. You'll be tested on data preprocessing techniques essential for data science.</p>
      <p>Perfect for Python developers looking to improve their data science skills or assess their knowledge of data cleaning techniques.</p>
      <p><a href="numpy_pandas_quiz.pdf" download><i class="fas fa-file-pdf"></i> Download this quiz as a PDF</a></p>
    </div>

    <!-- Wrap progress elements in a sticky container -->
    <div class="progress-wrapper">
      <div class="progress-container">
        <div id="progress-fill"></div>
      </div>
      <div id="progress-count">0/15 answered</div>
    </div>

    <form id="quiz-form">
      <!-- Question 1 -->
      <div class="question">
        <p>1. Why do we need to clean data?</p>
        <div class="options">
          <label><input type="radio" name="q1" value="A"> A. To make the dataset bigger</label>
          <label><input type="radio" name="q1" value="B"> B. To make better decisions and get accurate results</label>
          <label><input type="radio" name="q1" value="C"> C. To make the data look pretty</label>
          <label><input type="radio" name="q1" value="D"> D. To confuse the computer</label>
        </div>
      </div>

      <!-- Question 2 -->
      <div class="question">
        <p>2. Which of the following is an example of wrong data?</p>
        <div class="options">
          <label><input type="radio" name="q2" value="A"> A. Missing a student's score</label>
          <label><input type="radio" name="q2" value="B"> B. Recording a person's age as 200 years old</label>
          <label><input type="radio" name="q2" value="C"> C. Entering a person's name twice</label>
          <label><input type="radio" name="q2" value="D"> D. Dates in different formats</label>
        </div>
      </div>

      <!-- Question 3 -->
      <div class="question">
        <p>3. Duplicate rows in a DataFrame can cause misleading results.</p>
        <div class="options">
          <label><input type="radio" name="q3" value="true"> True</label>
          <label><input type="radio" name="q3" value="false"> False</label>
        </div>
      </div>

      <!-- Question 4 -->
      <div class="question">
        <p>4. Fill in the blank: Replace missing values (NaN) with 0 in a NumPy array.</p>
        <div class="code-block">
import numpy as np<br>
arr = np.array([1, 2, np.nan, 4])<br>
arr[np.isnan(arr)] = ____
        </div>
        <div class="options">
          <label><input type="radio" name="q4" value="A"> A. None</label>
          <label><input type="radio" name="q4" value="B"> B. 0</label>
          <label><input type="radio" name="q4" value="C"> C. np.nan</label>
          <label><input type="radio" name="q4" value="D"> D. "missing"</label>
        </div>
      </div>

      <!-- Question 5 -->
      <div class="question">
        <p>5. What does vectorized math mean in NumPy? Give one example.</p>
        <div class="options">
          <label><input type="radio" name="q5" value="A"> A. Performing operations on individual elements one by one</label>
          <label><input type="radio" name="q5" value="B"> B. Performing operations on entire arrays at once, like arr + 5</label>
          <label><input type="radio" name="q5" value="C"> C. Converting arrays to vectors</label>
          <label><input type="radio" name="q5" value="D"> D. Creating mathematical graphs</label>
        </div>
      </div>

      <!-- Question 6 -->
      <div class="question">
        <p>6. What does dtype=str do when creating a NumPy array?</p>
        <div class="options">
          <label><input type="radio" name="q6" value="A"> A. Forces all elements to be stored as strings</label>
          <label><input type="radio" name="q6" value="B"> B. Converts all strings to numbers</label>
          <label><input type="radio" name="q6" value="C"> C. Removes missing values automatically</label>
          <label><input type="radio" name="q6" value="D"> D. Makes the array faster</label>
        </div>
      </div>

      <!-- Question 7 -->
      <div class="question">
        <p>7. What will the following code output?</p>
        <div class="code-block">
import numpy as np<br>
arr = np.array([1, 2, 3, 4])<br>
print(arr + 5)
        </div>
        <div class="options">
          <label><input type="radio" name="q7" value="A"> A. [1, 2, 3, 4, 5]</label>
          <label><input type="radio" name="q7" value="B"> B. [6, 7, 8, 9]</label>
          <label><input type="radio" name="q7" value="C"> C. [5, 5, 5, 5]</label>
          <label><input type="radio" name="q7" value="D"> D. Error</label>
        </div>
      </div>

      <!-- Question 8 -->
      <div class="question">
        <p>8. What does np.empty_like(original) create?</p>
        <div class="options">
          <label><input type="radio" name="q8" value="A"> A. A copy of the original array with the same data</label>
          <label><input type="radio" name="q8" value="B"> B. A new array with the same shape and dtype, but uninitialized</label>
          <label><input type="radio" name="q8" value="C"> C. A DataFrame</label>
          <label><input type="radio" name="q8" value="D"> D. A fully empty DataFrame</label>
        </div>
      </div>

      <!-- Question 9 -->
      <div class="question">
        <p>9. What is a DataFrame in Pandas?</p>
        <div class="options">
          <label><input type="radio" name="q9" value="A"> A. A Python list</label>
          <label><input type="radio" name="q9" value="B"> B. A NumPy array</label>
          <label><input type="radio" name="q9" value="C"> C. A table with rows and columns</label>
          <label><input type="radio" name="q9" value="D"> D. A type of string</label>
        </div>
      </div>

      <!-- Question 10 -->
      <div class="question">
        <p>10. Fill in the missing code: Create a DataFrame from a dictionary.</p>
        <div class="code-block">
import pandas as pd<br>
data = {<br>
&nbsp;&nbsp;&nbsp;&nbsp;"Name": ["Alice", "Bob"],<br>
&nbsp;&nbsp;&nbsp;&nbsp;"Age": [12, 15]<br>
}<br>
df = pd.____(data)
        </div>
        <div class="options">
          <label><input type="radio" name="q10" value="A"> A. DataFrame</label>
          <label><input type="radio" name="q10" value="B"> B. create</label>
          <label><input type="radio" name="q10" value="C"> C. dict_to_df</label>
          <label><input type="radio" name="q10" value="D"> D. from_dict</label>
        </div>
      </div>

      <!-- Question 11 -->
      <div class="question">
        <p>11. Explain what this line of code does: df["Score"] = df["Score"].fillna(df["Score"].mean())</p>
        <div class="options">
          <label><input type="radio" name="q11" value="A"> A. Calculates the mean of all scores</label>
          <label><input type="radio" name="q11" value="B"> B. Replaces missing values in the Score column with the average score</label>
          <label><input type="radio" name="q11" value="C"> C. Removes all missing values</label>
          <label><input type="radio" name="q11" value="D"> D. Creates a new Score column</label>
        </div>
      </div>

      <!-- Question 12 -->
      <div class="question">
        <p>12. How can we replace missing author names in a DataFrame with "Unknown"?</p>
        <div class="options">
          <label><input type="radio" name="q12" value="A"> A. df['Author'].dropna()</label>
          <label><input type="radio" name="q12" value="B"> B. df['Author'].fillna("Unknown", inplace=True)</label>
          <label><input type="radio" name="q12" value="C"> C. df['Author'].replace("Unknown")</label>
          <label><input type="radio" name="q12" value="D"> D. df['Author'] = "Unknown"</label>
        </div>
      </div>

      <!-- Question 13 -->
      <div class="question">
        <p>13. Pandas automatically ignores NaN values when calculating the mean.</p>
        <div class="options">
          <label><input type="radio" name="q13" value="true"> True</label>
          <label><input type="radio" name="q13" value="false"> False</label>
        </div>
      </div>

      <!-- Question 14 -->
      <div class="question">
        <p>14. Which of the following best describes the advantage of Pandas over NumPy for data cleaning?</p>
        <div class="options">
          <label><input type="radio" name="q14" value="A"> A. Pandas can automatically handle tables with mixed data types.</label>
          <label><input type="radio" name="q14" value="B"> B. Pandas is always faster than NumPy.</label>
          <label><input type="radio" name="q14" value="C"> C. Pandas only works with numbers.</label>
          <label><input type="radio" name="q14" value="D"> D. Pandas doesn't support missing values.</label>
        </div>
      </div>

      <!-- Question 15 -->
      <div class="question">
        <p>15. You scraped this dataset:</p>
        <table style="border-collapse: collapse; margin: 10px 0;">
          <tr style="border: 1px solid #ddd;"><th style="border: 1px solid #ddd; padding: 8px;">Name</th><th style="border: 1px solid #ddd; padding: 8px;">Score</th></tr>
          <tr style="border: 1px solid #ddd;"><td style="border: 1px solid #ddd; padding: 8px;">Alice</td><td style="border: 1px solid #ddd; padding: 8px;">95</td></tr>
          <tr style="border: 1px solid #ddd;"><td style="border: 1px solid #ddd; padding: 8px;">Bob</td><td style="border: 1px solid #ddd; padding: 8px;">None</td></tr>
          <tr style="border: 1px solid #ddd;"><td style="border: 1px solid #ddd; padding: 8px;">Charlie</td><td style="border: 1px solid #ddd; padding: 8px;">88</td></tr>
          <tr style="border: 1px solid #ddd;"><td style="border: 1px solid #ddd; padding: 8px;">David</td><td style="border: 1px solid #ddd; padding: 8px;">None</td></tr>
        </table>
        <p>Write Pandas code to replace missing scores with the average score.</p>
        <div class="options">
          <label><input type="radio" name="q15" value="A"> A. df['Score'].fillna(df['Score'].mean())</label>
          <label><input type="radio" name="q15" value="B"> B. df['Score'].replace(None, df['Score'].average())</label>
          <label><input type="radio" name="q15" value="C"> C. df['Score'].dropna()</label>
          <label><input type="radio" name="q15" value="D"> D. df['Score'] = df['Score'].mean()</label>
        </div>
      </div>

      <button type="button" id="submitBtn" onclick="submitQuiz()">Submit Quiz</button>
      <div id="result"></div>

    </form>

    <!-- Add results section -->
    <div id="results" style="display: none;">
      <div class="score-container">
        <div class="score-circle">0</div>
        <div class="score-label">Your Score</div>
      </div>
      <div class="feedback"></div>
      <div class="answer-key">
        <h3>Answer Key</h3>
        <!-- Answer details will be inserted here -->
      </div>
    </div>
  </div>

  <script>
    const answers = {
      q1: "B",
      q2: "B",
      q3: "true",
      q4: "B",
      q5: "B",
      q6: "A",
      q7: "B",
      q8: "B",
      q9: "C",
      q10: "A",
      q11: "B",
      q12: "B",
      q13: "true",
      q14: "A",
      q15: "A"
    };

    // Track progress when radio buttons are selected
    const radioButtons = document.querySelectorAll('input[type="radio"]');
    radioButtons.forEach(radio => {
      radio.addEventListener('change', updateProgress);
    });

    function updateProgress() {
      // Count answered questions
      let answeredQuestions = 0;
      const totalQuestions = 15;

      for (let i = 1; i <= totalQuestions; i++) {
        if (document.querySelector(`input[name="q${i}"]:checked`)) {
          answeredQuestions++;
        }
      }

      // Update progress text and bar
      document.getElementById('progress-count').textContent = `${answeredQuestions}/${totalQuestions} answered`;
      const progressPercentage = (answeredQuestions / totalQuestions) * 100;
      document.getElementById('progress-fill').style.width = `${progressPercentage}%`;
    }

    function submitQuiz() {
      let score = 0;
      let answersHTML = '';

      // Check each question
      for (let key in answers) {
        const questionNumber = key.substring(1); // Extract number from q1, q2, etc.
        const selectedOption = document.querySelector(`input[name="${key}"]:checked`);

        // Get question text
        const questionText = document.querySelector(`[name="${key}"]`).closest('.question').querySelector('p').textContent;

        // Start answer item
        answersHTML += `<div class="answer-item ${selectedOption && selectedOption.value === answers[key] ? 'correct' : 'incorrect'}">`;
        answersHTML += `<h4>Question ${questionNumber}</h4>`;
        answersHTML += `<p>${questionText}</p>`;

        if (selectedOption) {
          const userAnswer = selectedOption.value;
          const isCorrect = userAnswer === answers[key];

          if (isCorrect) {
            score++;
            answersHTML += `<p class="user-answer">Your answer: ${userAnswer} - Correct</p>`;
          } else {
            answersHTML += `<p class="user-answer">Your answer: ${userAnswer} - Incorrect</p>`;
            answersHTML += `<p class="correct-answer">Correct answer: ${answers[key]}</p>`;
          }
        } else {
          answersHTML += `<p class="user-answer">Not answered</p>`;
          answersHTML += `<p class="correct-answer">Correct answer: ${answers[key]}</p>`;
        }

        answersHTML += `</div>`;
      }

      // Update score display
      document.querySelector('.score-circle').textContent = score;

      // Add feedback based on score
      const percentage = (score / Object.keys(answers).length) * 100;
      let feedbackText = '';

      if (percentage >= 90) {
        feedbackText = 'Excellent! You have a strong understanding of NumPy and Pandas data cleaning.';
      } else if (percentage >= 70) {
        feedbackText = 'Good job! You have a solid grasp of data cleaning concepts.';
      } else if (percentage >= 50) {
        feedbackText = 'Not bad. You have a basic understanding of data cleaning, but there\'s room for improvement.';
      } else {
        feedbackText = 'You might need more practice with NumPy and Pandas data cleaning concepts.';
      }

      document.querySelector('.feedback').textContent = feedbackText;

      // Add answers to the answer key section
      document.querySelector('.answer-key').innerHTML = `<h3>Answer Key</h3>${answersHTML}`;

      // Hide the quiz form and show results
      document.getElementById('quiz-form').style.display = 'none';
      document.getElementById('results').style.display = 'block';

      // Scroll to results
      document.getElementById('results').scrollIntoView({ behavior: 'smooth' });
    }

    // Share counter functionality
    function incrementShareCount() {
      let currentCount = parseInt(document.getElementById('share-counter').textContent);
      const newCount = currentCount + 1;
      document.getElementById('share-counter').textContent = newCount;
      localStorage.setItem('numpy-pandas-quiz-shares', newCount.toString());
    }

    // Initialize share counter from localStorage or set to 0
    document.addEventListener('DOMContentLoaded', function() {
      const shareCount = localStorage.getItem('numpy-pandas-quiz-shares') || '0';
      document.getElementById('share-counter').textContent = shareCount;
    });
  </script>
  <!-- Shared Header and Footer JavaScript -->
  <script src="../../shared/header-footer.js"></script>
</body>
</html>
