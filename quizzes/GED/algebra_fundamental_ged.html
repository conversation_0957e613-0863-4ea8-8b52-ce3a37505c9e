<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GED Math: Algebra Fundamentals Practice Quiz | Free GED Prep</title>
    <meta name="description" content="Free GED Math practice quiz on algebra fundamentals. Test your skills with 15 questions covering essential algebraic concepts for the GED test. Get instant feedback and explanations.">
    <meta name="keywords" content="GED math practice, algebra fundamentals, basic algebra, GED test prep, free GED practice, GED math questions, algebra equations, GED study guide">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <!-- Shared Header/Footer CSS -->
  <link rel="stylesheet" href="../../shared/header-footer.css">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <!-- Shared Header/Footer CSS -->
  <link rel="stylesheet" href="../../shared/header-footer.css"></noscript>
    
    <!-- Defer non-critical CSS -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js" as="script">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    
    <!-- Add structured data for rich results -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Quiz",
      "name": "GED Math: Algebra Fundamentals Practice Quiz",
      "description": "Free GED Math practice quiz with 15 questions on algebra fundamentals. Perfect for GED test prep with instant scoring and explanations.",
      "educationalAlignment": {
        "@type": "AlignmentObject",
        "alignmentType": "educationalSubject",
        "targetName": "Mathematics"
      },
      "educationalUse": "Practice Quiz",
      "audience": {
        "@type": "EducationalAudience",
        "educationalRole": "student"
      },
      "provider": {
        "@type": "Person",
        "name": "Faruk Hasan",
        "url": "https://faruk-hasan.com"
      }
    }
    </script>
    
    <!-- Inline critical CSS -->
    <style>
        :root {
            --primary-color: #3a6ea5;
            --secondary-color: #004e98;
            --accent-color: #ff6b35;
            --correct-color: #4caf50;
            --incorrect-color: #f44336;
            --neutral-color: #f5f5f5;
            --text-color: #333;
            --light-text: #666;
            --white: #fff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
      /* Body padding/margin removed to match resources.html header layout */
            background-color: #f0f2f5;
            color: var(--text-color);
            line-height: 1.6;
        }

        .quiz-container {
            max-width: 800px;
            margin: 2rem auto;
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .quiz-header {
            background-color: var(--primary-color);
            color: var(--white);
            padding: 1.5rem 2rem;
            text-align: center;
        }

        .quiz-header h1 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }

        .quiz-intro {
            background-color: var(--neutral-color);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            color: var(--text-color);
            width: 100%;
        }

        .quiz-progress {
            height: 6px;
            background-color: rgba(255, 255, 255, 0.3);
            margin-top: 1.5rem;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background-color: var(--accent-color);
            width: 0%;
            transition: width 0.3s ease;
        }

        .quiz-body {
            
        }

        .question-container {
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #eee;
        }

        .question-text {
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 1.5rem;
            color: var(--text-color);
        }

        .question-number {
            display: inline-block;
            background-color: var(--primary-color);
            color: var(--white);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            margin-right: 10px;
        }

        .options-container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .option {
            background-color: var(--neutral-color);
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            display: flex;
            align-items: center;
        }

        .option:hover {
            background-color: #e8e8e8;
        }

        .option.selected {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .option.correct {
            background-color: rgba(76, 175, 80, 0.2);
            border: 1px solid var(--correct-color);
        }

        .option.incorrect {
            background-color: rgba(244, 67, 54, 0.2);
            border: 1px solid var(--incorrect-color);
        }

        .option-letter {
            background-color: var(--primary-color);
            color: var(--white);
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: 500;
        }

        .option.selected .option-letter {
            background-color: var(--white);
            color: var(--primary-color);
        }

        .option-text {
            flex: 1;
        }

        .result-icon {
            display: none;
            margin-left: 10px;
            font-size: 1.2rem;
        }

        .correct .fa-check {
            color: var(--correct-color);
        }

        .incorrect .fa-times {
            color: var(--incorrect-color);
        }

        .quiz-footer {
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            border-top: 1px solid #eee;
        }

        .quiz-btn {
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            display: flex;
            align-items: center;
            transition: background-color 0.2s;
        }

        .quiz-btn:hover {
            background-color: var(--secondary-color);
        }

        .quiz-btn i {
            margin-left: 8px;
        }

        .results-container {
            display: none;
            padding: 2rem;
            text-align: center;
        }

        .results-score {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 1rem 0;
            color: var(--primary-color);
        }

        .results-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: var(--text-color);
        }

        .restart-btn {
            background-color: var(--accent-color);
            margin: 0 auto;
        }

        .results-details {
            text-align: left;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #eee;
        }

        .answer-item {
            margin-bottom: 1.5rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #eee;
        }

        .answer-item p {
            margin-bottom: 0.5rem;
        }

        .correct {
            color: var(--correct-color);
        }

        .incorrect {
            color: var(--incorrect-color);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .quiz-container {
                margin: 1rem;
                border-radius: 8px;
            }

            .quiz-header {
                padding: 1.2rem;
            }

            .quiz-header h1 {
                font-size: 1.5rem;
            }

            .quiz-body, .quiz-footer {
                padding: 1.2rem;
            }

            .question-text {
                font-size: 1.1rem;
            }

            .option {
                padding: 0.8rem;
            }
        }
        
        .faq-section, .related-resources {
            width: 100%;
            max-width: 800px;
            margin: 2rem auto;
            padding: 1.5rem;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .faq-section h2, .related-resources h2 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }
        
        .faq-item {
            margin-bottom: 1.5rem;
        }
        
        .faq-item h3 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: var(--secondary-color);
        }
        
        .related-resources ul {
            list-style: none;
            padding: 0;
        }
        
        .related-resources li {
            margin-bottom: 0.8rem;
        }
        
        .related-resources a {
            color: var(--primary-color);
            text-decoration: none;
            display: inline-block;
            padding: 0.3rem 0;
        }
        
        .related-resources a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="quiz-container">
        <div class="quiz-header">
            <h1>GED Math: Algebra Fundamentals Practice Quiz</h1>
            <p>Test your understanding of fundamental algebra concepts - essential skills for the GED Math test</p>
            
            <!-- Add SEO-friendly introduction -->
            <div class="quiz-intro">
                <p>Welcome to our free GED Math practice quiz on algebra fundamentals. This 15-question quiz helps you prepare for the GED Math test by focusing on essential algebraic concepts.</p>
                
                <p>This quiz covers solving basic equations, understanding how to manipulate equations, and working with variables - all critical skills for success on the GED Math test.</p>
                
                <p><strong>GED Math Test Info:</strong> Algebra makes up approximately 25-30% of the GED Math section (115 minutes, ~45 questions).</p>
            </div>
            
            <div class="quiz-progress">
                <div class="progress-bar" id="progress-bar"></div>
            </div>
        </div>

        <div class="quiz-body">
            <!-- Question 1 -->
            <div class="question-container" id="question-1">
                <div class="question-text">
                    <span class="question-number">1</span>
                    True or False: To solve for \(x\) in the equation \(x + 5 = 10\), you subtract 5 from both sides of the equation.
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">True</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">False</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 2 -->
            <div class="question-container" id="question-2">
                <div class="question-text">
                    <span class="question-number">2</span>
                    True or False: To isolate \(x\) in the equation \(2x = 8\), you divide both sides of the equation by 2.
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">True</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">False</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 3 -->
            <div class="question-container" id="question-3">
                <div class="question-text">
                    <span class="question-number">3</span>
                    What happens if you move a number from the right side of the equation to the left side?
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">You add it if it was being subtracted on the right side</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">You subtract it if it was being added on the right side</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">You multiply it if it was being divided on the right side</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">You divide it if it was being multiplied on the right side</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 4 -->
            <div class="question-container" id="question-4">
                <div class="question-text">
                    <span class="question-number">4</span>
                    True or False: In the equation \(3x - 2 = 10\), to solve for \(x\), you first add 2 to both sides.
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">True</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">False</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 5 -->
            <div class="question-container" id="question-5">
                <div class="question-text">
                    <span class="question-number">5</span>
                    True or False: To solve for \(x\) in the equation \(5x + 3 = 18\), you subtract 3 from both sides first.
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">True</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">False</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 6 -->
            <div class="question-container" id="question-6">
                <div class="question-text">
                    <span class="question-number">6</span>
                    How do you solve for \(x\) in the equation \(x - 4 = 6\)?
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">Add 4 to both sides</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">Subtract 4 from both sides</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">Multiply both sides by 4</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">Divide both sides by 4</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 7 -->
            <div class="question-container" id="question-7">
                <div class="question-text">
                    <span class="question-number">7</span>
                    If the equation is \(4x + 7 = 19\), what is the first step to solve for \(x\)?
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">Subtract 7 from both sides</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">Divide both sides by 7</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">Add 7 to both sides</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">Multiply both sides by 4</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 8 -->
            <div class="question-container" id="question-8">
                <div class="question-text">
                    <span class="question-number">8</span>
                    True or False: If you move a term with a positive sign to the other side of the equation, you change its sign to negative.
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">True</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">False</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 9 -->
            <div class="question-container" id="question-9">
                <div class="question-text">
                    <span class="question-number">9</span>
                    What happens when you divide both sides of the equation \(2x = 12\) by 2?
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">The equation becomes \(x = 6\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">The equation becomes \(x = 24\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">The equation becomes \(x = 2\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">The equation becomes \(x = 14\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 10 -->
            <div class="question-container" id="question-10">
                <div class="question-text">
                    <span class="question-number">10</span>
                    How do you work backward to solve for \(x\) in the equation \(3x + 4 = 13\)?
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">Subtract 4 from both sides, then divide by 3</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">Add 4 to both sides, then divide by 3</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">Divide both sides by 3, then add 4</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">Multiply both sides by 3, then subtract 4</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 11 -->
            <div class="question-container" id="question-11">
                <div class="question-text">
                    <span class="question-number">11</span>
                    True or False: To solve for \(x\) in the equation \(x/5 = 3\), you multiply both sides by 5.
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">True</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">False</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 12 -->
            <div class="question-container" id="question-12">
                <div class="question-text">
                    <span class="question-number">12</span>
                    How do you solve for \(x\) in the equation \(x + 6 = 12\)?
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">Subtract 6 from both sides</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">Add 6 to both sides</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">Multiply both sides by 6</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">Divide both sides by 6</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 13 -->
            <div class="question-container" id="question-13">
                <div class="question-text">
                    <span class="question-number">13</span>
                    True or False: When you move a term from one side of the equation to the other, you reverse the operation (add becomes subtract, multiply becomes divide).
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">True</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">False</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 14 -->
            <div class="question-container" id="question-14">
                <div class="question-text">
                    <span class="question-number">14</span>
                    What is the first step to solve the equation \(x/2 = 8\)?
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">Multiply both sides by 2</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">Divide both sides by 2</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">Add 2 to both sides</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">Subtract 2 from both sides</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 15 -->
            <div class="question-container" id="question-15">
                <div class="question-text">
                    <span class="question-number">15</span>
                    True or False: In the equation \(2x - 3 = 7\), you first add 3 to both sides to begin solving for \(x\).
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">True</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">False</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="quiz-footer">
            <button class="quiz-btn" id="prev-btn" disabled>Previous</button>
            <button class="quiz-btn" id="next-btn">Next</button>
        </div>
    </div>

    <div class="results-container">
        <div class="results-score" id="results-score"></div>
        <div class="results-message" id="results-message"></div>
        <button class="quiz-btn restart-btn" id="restart-btn">Restart Quiz</button>
        <div class="results-details" id="results-details"></div>
    </div>

    <script>
        const questions = [
            {
                question: "True or False: To solve for \(x\) in the equation \(x + 5 = 10\), you subtract 5 from both sides of the equation.",
                options: ["True", "False"],
                correct: "True"
            },
            {
                question: "True or False: To isolate \(x\) in the equation \(2x = 8\), you divide both sides of the equation by 2.",
                options: ["True", "False"],
                correct: "True"
            },
            {
                question: "What happens if you move a number from the right side of the equation to the left side?",
                options: [
                    "You add it if it was being subtracted on the right side",
                    "You subtract it if it was being added on the right side",
                    "You multiply it if it was being divided on the right side",
                    "You divide it if it was being multiplied on the right side"
                ],
                correct: "A"
            },
            {
                question: "True or False: In the equation \(3x - 2 = 10\), to solve for \(x\), you first add 2 to both sides.",
                options: ["True", "False"],
                correct: "True"
            },
            {
                question: "True or False: To solve for \(x\) in the equation \(5x + 3 = 18\), you subtract 3 from both sides first.",
                options: ["True", "False"],
                correct: "True"
            },
            {
                question: "How do you solve for \(x\) in the equation \(x - 4 = 6\)?",
                options: [
                    "Add 4 to both sides",
                    "Subtract 4 from both sides",
                    "Multiply both sides by 4",
                    "Divide both sides by 4"
                ],
                correct: "A"
            },
            {
                question: "If the equation is \(4x + 7 = 19\), what is the first step to solve for \(x\)?",
                options: [
                    "Subtract 7 from both sides",
                    "Divide both sides by 7",
                    "Add 7 to both sides",
                    "Multiply both sides by 4"
                ],
                correct: "A"
            },
            {
                question: "True or False: If you move a term with a positive sign to the other side of the equation, you change its sign to negative.",
                options: ["True", "False"],
                correct: "True"
            },
            {
                question: "What happens when you divide both sides of the equation \(2x = 12\) by 2?",
                options: [
                    "The equation becomes \(x = 6\)",
                    "The equation becomes \(x = 24\)",
                    "The equation becomes \(x = 2\)",
                    "The equation becomes \(x = 14\)"
                ],
                correct: "A"
            },
            {
                question: "How do you work backward to solve for \(x\) in the equation \(3x + 4 = 13\)?",
                options: [
                    "Subtract 4 from both sides, then divide by 3",
                    "Add 4 to both sides, then divide by 3",
                    "Divide both sides by 3, then add 4",
                    "Multiply both sides by 3, then subtract 4"
                ],
                correct: "A"
            },
            {
                question: "True or False: To solve for \(x\) in the equation \(x/5 = 3\), you multiply both sides by 5.",
                options: ["True", "False"],
                correct: "True"
            },
            {
                question: "How do you solve for \(x\) in the equation \(x + 6 = 12\)?",
                options: [
                    "Subtract 6 from both sides",
                    "Add 6 to both sides",
                    "Multiply both sides by 6",
                    "Divide both sides by 6"
                ],
                correct: "A"
            },
            {
                question: "True or False: When you move a term from one side of the equation to the other, you reverse the operation (add becomes subtract, multiply becomes divide).",
                options: ["True", "False"],
                correct: "True"
            },
            {
                question: "What is the first step to solve the equation \(x/2 = 8\)?",
                options: [
                    "Multiply both sides by 2",
                    "Divide both sides by 2",
                    "Add 2 to both sides",
                    "Subtract 2 from both sides"
                ],
                correct: "A"
            },
            {
                question: "True or False: In the equation \(2x - 3 = 7\), you first add 3 to both sides to begin solving for \(x\).",
                options: ["True", "False"],
                correct: "True"
            }
        ];

        let currentQuestionIndex = 0;
        let score = 0;

        const questionContainer = document.querySelector('.quiz-body');
        const progressContainer = document.querySelector('.quiz-progress');
        const progressBar = document.querySelector('.progress-bar');
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        const resultsContainer = document.querySelector('.results-container');
        const resultsScore = document.getElementById('results-score');
        const resultsMessage = document.getElementById('results-message');
        const restartBtn = document.getElementById('restart-btn');
        const resultsDetails = document.getElementById('results-details');

        function loadQuestion() {
            const question = questions[currentQuestionIndex];
            questionContainer.innerHTML = `
                <div class="question-text">
                    <span class="question-number">${currentQuestionIndex + 1}</span>
                    ${question.question}
                </div>
                <div class="options-container">
                    ${question.options.map((option, index) => `
                        <div class="option" data-option="${String.fromCharCode(65 + index)}">
                            <div class="option-letter">${String.fromCharCode(65 + index)}</div>
                            <div class="option-text">${option}</div>
                            <i class="fas fa-check result-icon correct"></i>
                            <i class="fas fa-times result-icon incorrect"></i>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function updateProgress() {
            const progress = ((currentQuestionIndex + 1) / questions.length) * 100;
            progressBar.style.width = `${progress}%`;
        }

        function showResults() {
            resultsContainer.style.display = 'block';
            resultsScore.textContent = `${score}/${questions.length}`;
            resultsMessage.textContent = score === questions.length ? "Congratulations! You've answered all questions correctly! 🎉" : "You've completed the quiz! Here's your score:";
            resultsDetails.innerHTML = questions.map((question, index) => `
                <div class="answer-item">
                    <p><strong>Question ${index + 1}:</strong> ${question.question}</p>
                    <p><strong>Your Answer:</strong> ${!question.selectedOption ? 'Not answered' : question.selectedOption}</p>
                    <p><strong>Correct Answer:</strong> ${!question.selectedOption ? 'Not answered' : question.correct}</p>
                </div>
            `).join('');
        }

        function resetQuiz() {
            currentQuestionIndex = 0;
            score = 0;
            questions.forEach(question => {
                question.selectedOption = null;
            });
            loadQuestion();
            updateProgress();
            resultsContainer.style.display = 'none';
        }

        function handleOptionClick(event) {
            const option = event.target.closest('.option');
            if (option) {
                const question = questions[currentQuestionIndex];
                const selectedOption = option.getAttribute('data-option');
                question.selectedOption = selectedOption;
                if (selectedOption === question.correct) {
                    score++;
                }
                option.classList.add('selected');
                option.classList.add('correct');
                option.querySelector('.result-icon.correct').style.display = 'block';
                option.querySelector('.result-icon.incorrect').style.display = 'none';
            }
        }

        function handleNext() {
            if (currentQuestionIndex < questions.length - 1) {
                currentQuestionIndex++;
                loadQuestion();
                updateProgress();
                prevBtn.disabled = false;
            } else {
                showResults();
            }
        }

        function handlePrev() {
            if (currentQuestionIndex > 0) {
                currentQuestionIndex--;
                loadQuestion();
                updateProgress();
                if (currentQuestionIndex === 0) {
                    prevBtn.disabled = true;
                }
            }
        }

        loadQuestion();
        updateProgress();

        questionContainer.addEventListener('click', handleOptionClick);
        nextBtn.addEventListener('click', handleNext);
        prevBtn.addEventListener('click', handlePrev);
        restartBtn.addEventListener('click', resetQuiz);
    </script>
  <!-- Shared Header and Footer JavaScript -->
  <script src="../../shared/header-footer.js"></script>
</body>
</html>
