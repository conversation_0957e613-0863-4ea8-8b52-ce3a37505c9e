<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GED Math Practice Tests & Quizzes | Free GED Prep Resources</title>
  <meta name="description" content="Free GED Math practice tests and quizzes covering all required topics: arithmetic, algebra, geometry, and data analysis. Prepare for your GED exam with our comprehensive practice materials.">
  <meta name="keywords" content="GED math practice, GED math test, GED practice test, free GED quizzes, GED math prep, GED algebra, GED geometry, GED data analysis">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="../styles.css">
  
  <!-- Add structured data for rich results -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "LearningResource",
    "name": "GED Math Practice Tests & Quizzes",
    "description": "Free GED Math practice tests and quizzes covering all required topics: arithmetic, algebra, geometry, and data analysis.",
    "educationalAlignment": {
      "@type": "AlignmentObject",
      "alignmentType": "educationalSubject",
      "targetName": "Mathematics"
    },
    "audience": {
      "@type": "EducationalAudience",
      "educationalRole": "student"
    },
    "learningResourceType": "Quiz",
    "isAccessibleForFree": true
  }
  </script>
  
  <style>
    :root {
      --primary-color: #7248b6;
      --primary-light: #f5f0ff;
      --primary-dark: #5a3a92;
      --secondary-color: #f8f9fa;
      --text-color: #333;
      --border-color: #e0e0e0;
      --success-color: #28a745;
      --box-shadow: 0 10px 20px rgba(0,0,0,0.05);
    }
    
    body {
      /* Body padding/margin removed to match resources.html header layout */
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--text-color);
      background-color: #f9f9f9;
      
      
    }
    
    .ged-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 40px 20px;
    }
    
    .ged-intro {
      background-color: white;
      padding: 35px;
      border-radius: 12px;
      margin-bottom: 40px;
      box-shadow: var(--box-shadow);
      position: relative;
      overflow: hidden;
    }
    
    .ged-intro::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 6px;
      height: 100%;
      background-color: var(--primary-color);
    }
    
    .ged-intro h1 {
      color: var(--primary-color);
      margin-top: 0;
      font-size: 2.2rem;
      margin-bottom: 20px;
    }
    
    .ged-intro p {
      font-size: 17px;
      line-height: 1.7;
      color: #555;
      margin-bottom: 15px;
    }
    
    .ged-categories {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 30px;
      margin-bottom: 50px;
    }
    
    .category-card {
      background-color: white;
      border-radius: 12px;
      box-shadow: var(--box-shadow);
      overflow: hidden;
      transition: transform 0.3s, box-shadow 0.3s;
    }
    
    .category-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 15px 30px rgba(0,0,0,0.1);
    }
    
    .category-header {
      background-color: var(--primary-color);
      color: white;
      padding: 20px;
      display: flex;
      align-items: center;
    }
    
    .category-header i {
      margin-right: 15px;
      font-size: 24px;
    }
    
    .category-header h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
    
    .category-content {
      padding: 20px;
    }
    
    .category-content ul {
      list-style-type: none;
      padding: 0;
      margin: 0;
    }
    
    .category-content li {
      margin-bottom: 12px;
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 12px;
    }
    
    .category-content li:last-child {
      margin-bottom: 0;
      border-bottom: none;
      padding-bottom: 0;
    }
    
    .category-content a {
      display: block;
      color: #444;
      text-decoration: none;
      padding: 10px 12px;
      border-radius: 6px;
      transition: all 0.2s;
      font-weight: 500;
    }
    
    .category-content a:hover {
      background-color: var(--primary-light);
      color: var(--primary-color);
      transform: translateX(5px);
    }
    
    .practice-tests {
      background-color: white;
      padding: 35px;
      border-radius: 12px;
      margin-top: 30px;
      box-shadow: var(--box-shadow);
    }
    
    .practice-tests h2 {
      color: var(--primary-color);
      margin-top: 0;
      font-size: 1.8rem;
      margin-bottom: 20px;
      position: relative;
      padding-bottom: 12px;
    }
    
    .practice-tests h2::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 60px;
      height: 3px;
      background-color: var(--primary-color);
    }
    
    .practice-tests p {
      font-size: 17px;
      margin-bottom: 25px;
      color: #555;
    }
    
    .test-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 25px;
    }
    
    .test-card {
      background-color: var(--secondary-color);
      border-radius: 12px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.05);
      padding: 25px;
      display: flex;
      flex-direction: column;
      transition: transform 0.3s, box-shadow 0.3s;
      border: 1px solid var(--border-color);
    }
    
    .test-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 20px rgba(0,0,0,0.1);
    }
    
    .test-card h3 {
      margin-top: 0;
      color: var(--primary-color);
      font-size: 1.3rem;
      margin-bottom: 15px;
    }
    
    .test-card p {
      flex-grow: 1;
      margin-bottom: 20px;
      color: #555;
      font-size: 16px;
    }
    
    .test-card a {
      display: inline-block;
      background-color: var(--primary-color);
      color: white;
      padding: 12px 20px;
      text-decoration: none;
      border-radius: 6px;
      font-weight: 500;
      text-align: center;
      transition: background-color 0.2s;
    }
    
    .test-card a:hover {
      background-color: var(--primary-dark);
    }
    
    .back-link {
      display: inline-block;
      margin-top: 40px;
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
      padding: 10px 15px;
      border-radius: 6px;
      transition: background-color 0.2s;
    }
    
    .back-link:hover {
      background-color: var(--primary-light);
    }
    
    .back-link i {
      margin-right: 8px;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
      .ged-categories {
        grid-template-columns: 1fr;
      }
      
      .test-grid {
        grid-template-columns: 1fr;
      }
      
      .ged-intro {
        padding: 25px;
      }
      
      .ged-intro h1 {
        font-size: 1.8rem;
      }
    }
  </style>
</head>
<body>
  <div class="ged-container">
    <div class="ged-intro">
      <h1>GED Math Practice Tests & Quizzes</h1>
      <p>Welcome to our comprehensive collection of GED Math practice tests and quizzes. These free resources are designed to help you prepare for the GED Math test by covering all the key topics you'll need to master. Choose a category below to start practicing!</p>
      <p><strong>The GED Math test covers four main areas:</strong> Basic Math/Number Operations (25-30%), Algebraic Concepts (30%), Geometry (20%), and Data Analysis & Probability (15-20%). Our practice tests are aligned with the official GED test format to give you the most realistic preparation experience.</p>
    </div>
    
    <div class="ged-categories">
      <!-- Basic Math / Number Operations -->
      <div class="category-card">
        <div class="category-header">
          <i class="fas fa-calculator"></i>
          <h2>Basic Math / Number Operations</h2>
        </div>
        <div class="category-content">
          <ul>
            <li><a href="GED/basic_arithmetic_operations.html">Basic Arithmetic Operations</a></li>
            <li><a href="number_puzzle_1.html">Number Puzzles</a></li>
            <li><a href="ged-fractions-decimals.html">Fractions & Decimals</a></li>
            <li><a href="ged-factors-multiples.html">Factors, Multiples & Prime Numbers</a></li>
            <li><a href="ged-ratios-proportions.html">Ratios, Proportions & Rates</a></li>
            <li><a href="ged-percents.html">Percents & Applications</a></li>
            <li><a href="ged-real-numbers.html">Real Number Operations</a></li>
          </ul>
        </div>
      </div>
      
      <!-- Algebraic Concepts -->
      <div class="category-card">
        <div class="category-header">
          <i class="fas fa-superscript"></i>
          <h2>Algebraic Concepts</h2>
        </div>
        <div class="category-content">
          <ul>
            <li><a href="GED/algebra_fundamental_ged.html">Algebra Fundamentals Quiz</a></li>
            <li><a href="GED/algebra_basic_ged.html">Basic Algebra Practice</a></li>
            
            <li><a href="ged-expressions-equations.html">Expressions & Equations</a></li>
            <li><a href="ged-linear-equations.html">Linear Equations & Functions</a></li>
            <li><a href="GED/system_of_equations.html">Systems of Equations</a></li>
            <li><a href="ged-exponents-polynomials.html">Exponents & Polynomials</a></li>
            <li><a href="ged-word-problems.html">Algebraic Word Problems</a></li>
          </ul>
        </div>
      </div>
      
      <!-- Geometry -->
      <div class="category-card">
        <div class="category-header">
          <i class="fas fa-shapes"></i>
          <h2>Geometry</h2>
        </div>
        <div class="category-content">
          <ul>
            <li><a href="ged-geometry.html">Geometry & Measurement</a></li>
            <li><a href="ged-area-volume.html">Area, Perimeter & Volume</a></li>
            <li><a href="ged-pythagorean.html">Pythagorean Theorem</a></li>
            <li><a href="ged-coordinate-geometry.html">Coordinate Geometry</a></li>
            <li><a href="ged-geometric-properties.html">Properties of Geometric Figures</a></li>
          </ul>
        </div>
      </div>
      
      <!-- Data Analysis & Probability -->
      <div class="category-card">
        <div class="category-header">
          <i class="fas fa-chart-bar"></i>
          <h2>Data Analysis & Probability</h2>
        </div>
        <div class="category-content">
          <ul>
            <li><a href="ged-data.html">Data Analysis & Statistics</a></li>
            <li><a href="ged-graphs-charts.html">Interpreting Graphs & Charts</a></li>
            <li><a href="ged-central-tendency.html">Mean, Median, Mode & Range</a></li>
            <li><a href="ged-probability.html">Probability Concepts</a></li>
            <li><a href="ged-scatter-plots.html">Scatter Plots & Correlation</a></li>
          </ul>
        </div>
      </div>
    </div>
    
    <!-- Practice Tests Section -->
    <div class="practice-tests">
      <h2>Complete Practice Tests</h2>
      <p>Ready to test your overall knowledge? Try one of our comprehensive practice tests that cover all GED Math topics. These tests simulate the actual GED exam format and difficulty level:</p>
      
      <div class="test-grid">
        <div class="test-card">
          <h3>Advanced GED Math Practice</h3>
          <p>A comprehensive test covering all GED Math topics with challenging questions similar to the actual exam. Includes instant scoring and detailed explanations.</p>
          <a href="ged_prep_test_1.html">Start Test</a>
        </div>
        
        <div class="test-card">
          <h3>GED Math Mini Test 1</h3>
          <p>A shorter practice test focusing on basic math operations and algebraic concepts. Perfect for beginners or quick review sessions.</p>
          <a href="ged-mini-test-1.html">Start Test</a>
        </div>
        
        <div class="test-card">
          <h3>GED Math Mini Test 2</h3>
          <p>A shorter practice test focusing on geometry and data analysis topics. Great for targeted practice in these specific areas.</p>
          <a href="ged-mini-test-2.html">Start Test</a>
        </div>
      </div>
    </div>
    
    <a href="../resources.html" class="back-link"><i class="fas fa-arrow-left"></i> Back to All Resources</a>
  </div>

  <script>
    // Add tracking for quiz selection
    document.addEventListener('DOMContentLoaded', function() {
      const quizLinks = document.querySelectorAll('.category-content a, .test-card a');
      
      quizLinks.forEach(link => {
        link.addEventListener('click', function(e) {
          // Track quiz selection (can be connected to analytics)
          const quizName = this.textContent;
          const quizUrl = this.getAttribute('href');
          console.log(`Quiz selected: ${quizName}, URL: ${quizUrl}`);
          
          // You could add Google Analytics or other tracking here
          // Example: gtag('event', 'select_quiz', { 'quiz_name': quizName });
        });
      });
    });
  </script>
</body>
</html>
