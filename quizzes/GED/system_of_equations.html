<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GED Math: Systems of Equations Practice Test | Free GED Math Quiz</title>
  <meta name="description" content="Free GED Math practice test with 15 systems of equations questions ranging from easy to hard. Perfect for GED test prep with instant scoring and explanations.">
  <meta name="keywords" content="GED math practice test, systems of equations, GED test prep, free GED practice, GED math questions, GED algebra, linear equations">
  
  <!-- Open Graph tags for social sharing -->
  <meta property="og:title" content="GED Math: Systems of Equations Practice Test | Free GED Math Quiz">
  <meta property="og:description" content="Test your GED math skills with this free systems of equations practice quiz. Get instant feedback and explanations.">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://faruk-hasan.com/quizzes/ged_systems_of_equations.html">
  
  <!-- Canonical URL to prevent duplicate content issues -->
  <link rel="canonical" href="https://faruk-hasan.com/quizzes/ged_systems_of_equations.html">
  
  <!-- Add structured data for rich results -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Quiz",
    "name": "GED Math: Systems of Equations Practice Test",
    "description": "Free GED Math practice test with 15 systems of equations questions ranging from easy to hard. Perfect for GED test prep with instant scoring and explanations.",
    "educationalAlignment": {
      "@type": "AlignmentObject",
      "alignmentType": "educationalSubject",
      "targetName": "Mathematics"
    },
    "educationalUse": "Practice Quiz",
    "audience": {
      "@type": "EducationalAudience",
      "educationalRole": "student"
    },
    "provider": {
      "@type": "Person",
      "name": "Faruk Hasan",
      "url": "https://faruk-hasan.com"
    },
    "about": [
      "GED Math",
      "Algebra",
      "Systems of Equations",
      "Linear Equations",
      "Test Preparation"
    ]
  }
  </script>
  
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Shared Header/Footer CSS -->
  <link rel="stylesheet" href="../../shared/header-footer.css">
  <!-- Add MathJax for LaTeX rendering -->
  <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
  <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
  <style>
    :root {
      --primary-color: #0077b5;
      --primary-dark: #005885;
      --secondary-color: #f5f7fa;
      --text-color: #333;
      --light-gray: #f5f5f5;
      --border-color: #e0e0e0;
      --success-color: #27ae60;
      --error-color: #e74c3c;
      --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    body {
      /* Body padding/margin removed to match resources.html header layout */
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      
      
      background-color: var(--secondary-color);
      color: var(--text-color);
    }
    
    h1 {
      text-align: center;
      color: var(--primary-color);
      margin-bottom: 30px;
      font-size: 2.2rem;
      border-bottom: 2px solid var(--border-color);
      padding-bottom: 10px;
    }
    
    .quiz-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 10px;
      box-shadow: var(--box-shadow);
      padding: 25px;
    }
    
    .question {
      margin-bottom: 25px;
      padding: 20px;
      background: #eef2f5;
      border-left: 4px solid var(--primary-color);
      border-radius: 5px;
      transition: transform 0.2s ease, box-shadow 0.3s ease;
    }
    
    .question:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    }
    
    .question-header {
      font-weight: bold;
      font-size: 1.1em;
      margin-bottom: 15px;
      color: var(--primary-dark);
    }
    
    .math-content {
      margin-bottom: 15px;
      padding: 10px;
      background: white;
      border-radius: 5px;
      border: 1px solid var(--border-color);
    }
    
    .options {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
    }
    
    .option {
      padding: 10px;
      border: 1px solid var(--border-color);
      border-radius: 5px;
      cursor: pointer;
      transition: background-color 0.2s;
      display: flex;
      align-items: center;
    }
    
    .option:hover {
      background-color: #f0f7ff;
    }
    
    .option input[type="radio"] {
      margin-right: 10px;
    }
    
    .submit-btn {
      display: block;
      width: 100%;
      padding: 12px;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 5px;
      font-size: 1.1em;
      cursor: pointer;
      margin-top: 20px;
      transition: background-color 0.3s;
    }
    
    .submit-btn:hover {
      background-color: var(--primary-dark);
    }
    
    #results {
      margin-top: 30px;
      text-align: center;
      padding: 20px;
      background: white;
      border-radius: 10px;
      box-shadow: var(--box-shadow);
      display: none;
    }
    
    .score-container {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 20px 0;
    }
    
    .score-circle {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      background-color: var(--primary-color);
      color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 2.5em;
      font-weight: bold;
      margin-right: 15px;
    }
    
    .score-label {
      font-size: 1.5em;
      color: var(--text-color);
    }
    
    .feedback {
      font-size: 1.2em;
      color: var(--primary-dark);
      margin-top: 15px;
    }
    
    /* Add progress bar styles */
    .progress-wrapper {
      position: sticky;
      top: 0;
      background-color: white;
      padding-top: 15px;
      padding-bottom: 5px;
      z-index: 100;
      border-bottom: 1px solid var(--border-color);
      margin-bottom: 15px;
    }
    
    .progress-container {
      width: 100%;
      background-color: #f0f0f0;
      border-radius: 5px;
      margin-bottom: 10px;
      height: 10px;
      overflow: hidden;
    }
    
    #progress-fill {
      height: 100%;
      background-color: var(--primary-color);
      width: 0%;
      transition: width 0.3s ease;
    }
    
    #progress-count {
      text-align: right;
      font-size: 0.9em;
      margin-bottom: 5px;
      color: var(--primary-dark);
    }
    
    /* Add styles for answer review */
    .answer-key {
      margin-top: 30px;
      text-align: left;
    }
    
    .answer-item {
      background: #f9f9f9;
      padding: 15px;
      margin-bottom: 15px;
      border-radius: 5px;
      border-left: 3px solid var(--primary-color);
    }
    
    .correct {
      color: var(--success-color);
      font-weight: bold;
    }
    
    .incorrect {
      color: var(--error-color);
      font-weight: bold;
    }
    
    /* Add styles for the intro section */
    .intro-section {
      margin-bottom: 25px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 5px;
      border-left: 4px solid var(--primary-color);
      line-height: 1.6;
    }
    
    .intro-section p {
      margin-bottom: 10px;
    }
    
    .intro-section p:last-child {
      margin-bottom: 0;
    }
    
    /* Difficulty level badges */
    .difficulty {
      display: inline-block;
      padding: 3px 8px;
      border-radius: 3px;
      font-size: 0.8em;
      margin-left: 10px;
      color: white;
    }
    
    .easy {
      background-color: #27ae60;
    }
    
    .medium {
      background-color: #f39c12;
    }
    
    .hard {
      background-color: #e74c3c;
    }
    
    /* FAQ styles */
    .faq-list {
      margin: 0;
      padding: 0;
    }
    
    .faq-list dt {
      font-weight: bold;
      color: var(--primary-dark);
      margin-top: 20px;
      font-size: 1.1em;
      border-left: 3px solid var(--primary-color);
      padding-left: 10px;
    }
    
    .faq-list dd {
      margin-left: 0;
      margin-top: 8px;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid var(--border-color);
      line-height: 1.6;
    }
    
    .faq-list dd:last-child {
      border-bottom: none;
    }
    
    /* Related resources styles */
    .related-resources {
      max-width: 800px;
      margin: 30px auto;
      padding: 20px;
      background-color: white;
      border-radius: 10px;
      box-shadow: var(--box-shadow);
    }
    
    .related-resources h3 {
      color: var(--primary-color);
      margin-bottom: 15px;
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 10px;
    }
    
    .related-resources ul {
      list-style-type: none;
      padding: 0;
    }
    
    .related-resources li {
      margin-bottom: 10px;
    }
    
    .related-resources a {
      color: var(--primary-color);
      text-decoration: none;
      transition: color 0.2s;
    }
    
    .related-resources a:hover {
      color: var(--primary-dark);
      text-decoration: underline;
    }
    
    /* Share buttons */
    .share-buttons {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 10px;
      margin-top: 20px;
    }
    
    .share-btn {
      display: inline-flex;
      align-items: center;
      padding: 8px 15px;
      border-radius: 5px;
      color: white;
      text-decoration: none;
      font-size: 0.9em;
      transition: opacity 0.2s;
    }
    
    .share-btn:hover {
      opacity: 0.9;
    }
    
    .share-btn i {
      margin-right: 5px;
    }
    
    .facebook {
      background-color: #3b5998;
    }
    
    .twitter {
      background-color: #1da1f2;
    }
    
    .linkedin {
      background-color: #0077b5;
    }
    
    .reddit {
      background-color: #ff4500;
    }
    
    .email {
      background-color: #777;
    }
    
    @media (max-width: 600px) {
      .options {
        grid-template-columns: 1fr;
      }
      
      .share-buttons {
        flex-direction: column;
        width: 100%;
      }
      
      .share-btn {
        width: 100%;
        justify-content: center;
      }
    }
  </style>
</head>
<body>

  <h1>GED Math: Systems of Equations Practice Test</h1>
  
  <div class="quiz-container">
    <!-- Add an introduction paragraph for SEO -->
    <div class="intro-section">
      <h2 style="text-align:center;">15 Systems of Equations Practice Questions</h2>
      <p>This free GED Math practice test contains 15 systems of equations questions ranging from easy to hard. The quiz covers solving linear systems, word problems, and applications of systems of equations. Complete the test to receive instant feedback and explanations for each question.</p>
      <p>Perfect for GED test preparation and assessing your readiness for the algebra section of the GED exam.</p>
      <p><a href="ged_systems_of_equations.pdf" download><i class="fas fa-file-pdf"></i> Download this quiz as a PDF</a></p>
    </div>
    
    <!-- Wrap progress elements in a sticky container -->
    <div class="progress-wrapper">
      <div class="progress-container">
        <div id="progress-fill"></div>
      </div>
      <div id="progress-count">0/15 answered</div>
    </div>
    
    <form id="quiz-form">
      <!-- Easy Problems -->
      <div class="question math">
        <div class="question-header">1. Solve the system: <span class="difficulty easy">Easy</span></div>
        <div class="math-content">
          \( x + y = 10 \)
          <br>
          \( x - y = 2 \)
        </div>
        <div class="question-text">What is the value of \(x\) and \(y\)?</div>
        <div class="options">
          <label class="option"><input type="radio" name="q1" value="A"> A. \(x = 6, y = 4\)</label>
          <label class="option"><input type="radio" name="q1" value="B"> B. \(x = 8, y = 2\)</label>
          <label class="option"><input type="radio" name="q1" value="C"> C. \(x = 4, y = 6\)</label>
          <label class="option"><input type="radio" name="q1" value="D"> D. \(x = 5, y = 5\)</label>
        </div>
      </div>

      <div class="question math">
        <div class="question-header">2. Solve the system: <span class="difficulty easy">Easy</span></div>
        <div class="math-content">
          \( 3x + 4y = 12 \)
          <br>
          \( 2x - y = 4 \)
        </div>
        <div class="question-text">What is the value of \(x\) and \(y\)?</div>
        <div class="options">
          <label class="option"><input type="radio" name="q2" value="A"> A. \(x = 2, y = 1\)</label>
          <label class="option"><input type="radio" name="q2" value="B"> B. \(x = 1, y = 2\)</label>
          <label class="option"><input type="radio" name="q2" value="C"> C. \(x = 3, y = 0\)</label>
          <label class="option"><input type="radio" name="q2" value="D"> D. \(x = 0, y = 3\)</label>
        </div>
      </div>

      <div class="question math">
        <div class="question-header">3. Solve the system: <span class="difficulty easy">Easy</span></div>
        <div class="math-content">
          \( 2x + 3y = 13 \)
          <br>
          \( x - y = 1 \)
        </div>
        <div class="question-text">What is the value of \(x\) and \(y\)?</div>
        <div class="options">
          <label class="option"><input type="radio" name="q3" value="A"> A. \(x = 5, y = 4\)</label>
          <label class="option"><input type="radio" name="q3" value="B"> B. \(x = 4, y = 3\)</label>
          <label class="option"><input type="radio" name="q3" value="C"> C. \(x = 3, y = 2\)</label>
          <label class="option"><input type="radio" name="q3" value="D"> D. \(x = 6, y = 1\)</label>
        </div>
      </div>

      <div class="question math">
        <div class="question-header">4. Solve the system: <span class="difficulty easy">Easy</span></div>
        <div class="math-content">
          \( 4x + y = 7 \)
          <br>
          \( 3x - y = 8 \)
        </div>
        <div class="question-text">What is the value of \(x\) and \(y\)?</div>
        <div class="options">
          <label class="option"><input type="radio" name="q4" value="A"> A. \(x = 2, y = -1\)</label>
          <label class="option"><input type="radio" name="q4" value="B"> B. \(x = 1, y = 3\)</label>
          <label class="option"><input type="radio" name="q4" value="C"> C. \(x = 3, y = -2\)</label>
          <label class="option"><input type="radio" name="q4" value="D"> D. \(x = 4, y = -1\)</label>
        </div>
      </div>

      <div class="question math">
        <div class="question-header">5. Solve the system: <span class="difficulty easy">Easy</span></div>
        <div class="math-content">
          \( x + 2y = 6 \)
          <br>
          \( 3x - y = 7 \)
        </div>
        <div class="question-text">What is the value of \(x\) and \(y\)?</div>
        <div class="options">
          <label class="option"><input type="radio" name="q5" value="A"> A. \(x = 2, y = 2\)</label>
          <label class="option"><input type="radio" name="q5" value="B"> B. \(x = 4, y = 1\)</label>
          <label class="option"><input type="radio" name="q5" value="C"> C. \(x = 5, y = 0\)</label>
          <label class="option"><input type="radio" name="q5" value="D"> D. \(x = 1, y = 3\)</label>
        </div>
      </div>

      <!-- Medium Problems -->
      <div class="question math">
        <div class="question-header">6. Solve the system: <span class="difficulty medium">Medium</span></div>
        <div class="math-content">
          \( 2x - y = 3 \)
          <br>
          \( 5x + 3y = 19 \)
        </div>
        <div class="question-text">What is the value of \(x\) and \(y\)?</div>
        <div class="options">
          <label class="option"><input type="radio" name="q6" value="A"> A. \(x = 2, y = 1\)</label>
          <label class="option"><input type="radio" name="q6" value="B"> B. \(x = 3, y = 3\)</label>
          <label class="option"><input type="radio" name="q6" value="C"> C. \(x = 1, y = -1\)</label>
          <label class="option"><input type="radio" name="q6" value="D"> D. \(x = 4, y = 5\)</label>
        </div>
      </div>

      <div class="question math">
        <div class="question-header">7. Solve the system: <span class="difficulty medium">Medium</span></div>
        <div class="math-content">
          \( 3x + 2y = 10 \)
          <br>
          \( x - y = 4 \)
        </div>
        <div class="question-text">What is the value of \(x\) and \(y\)?</div>
        <div class="options">
          <label class="option"><input type="radio" name="q7" value="A"> A. \(x = 5, y = 1\)</label>
          <label class="option"><input type="radio" name="q7" value="B"> B. \(x = 6, y = 2\)</label>
          <label class="option"><input type="radio" name="q7" value="C"> C. \(x = 3, y = -1\)</label>
          <label class="option"><input type="radio" name="q7" value="D"> D. \(x = 2, y = 2\)</label>
        </div>
      </div>

      <div class="question math">
        <div class="question-header">8. Solve the system: <span class="difficulty medium">Medium</span></div>
        <div class="math-content">
          \( 4x + y = 11 \)
          <br>
          \( 2x + 3y = 16 \)
        </div>
        <div class="question-text">What is the value of \(x\) and \(y\)?</div>
        <div class="options">
          <label class="option"><input type="radio" name="q8" value="A"> A. \(x = 3, y = -1\)</label>
          <label class="option"><input type="radio" name="q8" value="B"> B. \(x = 2, y = 3\)</label>
          <label class="option"><input type="radio" name="q8" value="C"> C. \(x = 2, y = 4\)</label>
          <label class="option"><input type="radio" name="q8" value="D"> D. \(x = 5, y = 0\)</label>
        </div>
      </div>

      <div class="question math">
        <div class="question-header">9. Solve the system: <span class="difficulty medium">Medium</span></div>
        <div class="math-content">
          \( 5x + 2y = 12 \)
          <br>
          \( 3x + y = 7 \)
        </div>
        <div class="question-text">What is the value of \(x\) and \(y\)?</div>
        <div class="options">
          <label class="option"><input type="radio" name="q9" value="A"> A. \(x = 2, y = 1\)</label>
          <label class="option"><input type="radio" name="q9" value="B"> B. \(x = 1, y = 4\)</label>
          <label class="option"><input type="radio" name="q9" value="C"> C. \(x = 3, y = -2\)</label>
          <label class="option"><input type="radio" name="q9" value="D"> D. \(x = 0, y = 6\)</label>
        </div>
      </div>

      <div class="question math">
        <div class="question-header">10. Word problem: <span class="difficulty medium">Medium</span></div>
        <div class="math-content">
          A farmer has 50 chickens and ducks in total. The total number of legs of the chickens and ducks is 140.
          <br><br>
          \( x + y = 50 \)
          <br>
          \( 2x + 4y = 140 \)
        </div>
        <div class="question-text">How many chickens and how many ducks are there?</div>
        <div class="options">
          <label class="option"><input type="radio" name="q10" value="A"> A. 20 chickens, 30 ducks</label>
          <label class="option"><input type="radio" name="q10" value="B"> B. 25 chickens, 25 ducks</label>
          <label class="option"><input type="radio" name="q10" value="C"> C. 30 chickens, 20 ducks</label>
          <label class="option"><input type="radio" name="q10" value="D"> D. 35 chickens, 15 ducks</label>
        </div>
      </div>

      <!-- Hard Problems -->
      <div class="question math">
        <div class="question-header">11. Word problem: <span class="difficulty hard">Hard</span></div>
        <div class="math-content">
          Two numbers have a sum of 18. The difference between the two numbers is 6.
          <br><br>
          \( x + y = 18 \)
          <br>
          \( x - y = 6 \)
        </div>
        <div class="question-text">What are the two numbers?</div>
        <div class="options">
          <label class="option"><input type="radio" name="q11" value="A"> A. \(x = 12, y = 6\)</label>
          <label class="option"><input type="radio" name="q11" value="B"> B. \(x = 10, y = 8\)</label>
          <label class="option"><input type="radio" name="q11" value="C"> C. \(x = 9, y = 9\)</label>
          <label class="option"><input type="radio" name="q11" value="D"> D. \(x = 8, y = 10\)</label>
        </div>
      </div>

      <div class="question math">
        <div class="question-header">12. Solve the system: <span class="difficulty hard">Hard</span></div>
        <div class="math-content">
          \( 3x - 2y = 7 \)
          <br>
          \( 5x + 4y = 23 \)
        </div>
        <div class="question-text">What is the value of \(x\) and \(y\)?</div>
        <div class="options">
          <label class="option"><input type="radio" name="q12" value="A"> A. \(x = 3, y = 2\)</label>
          <label class="option"><input type="radio" name="q12" value="B"> B. \(x = 4, y = 1\)</label>
          <label class="option"><input type="radio" name="q12" value="C"> C. \(x = 5, y = 4\)</label>
          <label class="option"><input type="radio" name="q12" value="D"> D. \(x = 2, y = -1\)</label>
        </div>
      </div>

      <div class="question math">
        <div class="question-header">13. Word problem: <span class="difficulty hard">Hard</span></div>
        <div class="math-content">
          A store sells pencils for $0.50 each and erasers for $1.20 each. A customer buys a total of 12 items and spends $9.60.
          <br><br>
          \( x + y = 12 \)
          <br>
          \( 0.5x + 1.2y = 9.6 \)
        </div>
        <div class="question-text">How many pencils and erasers did they buy?</div>
        <div class="options">
          <label class="option"><input type="radio" name="q13" value="A"> A. 6 pencils, 6 erasers</label>
          <label class="option"><input type="radio" name="q13" value="B"> B. 8 pencils, 4 erasers</label>
          <label class="option"><input type="radio" name="q13" value="C"> C. 10 pencils, 2 erasers</label>
          <label class="option"><input type="radio" name="q13" value="D"> D. 4 pencils, 8 erasers</label>
        </div>
      </div>

      <div class="question math">
        <div class="question-header">14. Solve the system: <span class="difficulty hard">Hard</span></div>
        <div class="math-content">
          \( 2x + 3y = 11 \)
          <br>
          \( 5x - 2y = 12 \)
        </div>
        <div class="question-text">What is the value of \(x\) and \(y\)?</div>
        <div class="options">
          <label class="option"><input type="radio" name="q14" value="A"> A. \(x = 2, y = 3\)</label>
          <label class="option"><input type="radio" name="q14" value="B"> B. \(x = 3, y = 2\)</label>
          <label class="option"><input type="radio" name="q14" value="C"> C. \(x = 1, y = 3\)</label>
          <label class="option"><input type="radio" name="q14" value="D"> D. \(x = 4, y = 1\)</label>
        </div>
      </div>

      <div class="question math">
        <div class="question-header">15. Word problem: <span class="difficulty hard">Hard</span></div>
        <div class="math-content">
          A group of students raised $360 for a charity. The group sold 3 tickets for every 2 sold by another group. If both groups together sold 120 tickets, how many tickets did each group sell?
          <br><br>
          \( x + y = 120 \)
          <br>
          \( 3x = 2y \)
        </div>
        <div class="question-text">What is the number of tickets sold by each group?</div>
        <div class="options">
          <label class="option"><input type="radio" name="q15" value="A"> A. 40 tickets by group 1, 80 tickets by group 2</label>
          <label class="option"><input type="radio" name="q15" value="B"> B. 50 tickets by group 1, 70 tickets by group 2</label>
          <label class="option"><input type="radio" name="q15" value="C"> C. 60 tickets by group 1, 60 tickets by group 2</label>
          <label class="option"><input type="radio" name="q15" value="D"> D. 70 tickets by group 1, 50 tickets by group 2</label>
        </div>
      </div>
      
      <!-- Submit button -->
      <button type="button" id="submit-btn" class="submit-btn">
        <i class="fas fa-check-circle"></i> Submit Quiz
      </button>
    </form>
    
    <!-- Results section -->
    <div id="results">
      <h2>Your Results</h2>
      <div class="score-container">
        <div class="score-circle" id="score">0</div>
        <div class="score-label">out of 15</div>
      </div>
      <div class="feedback" id="feedback"></div>
      <div class="answer-key" id="answer-key">
        <h3>Answer Key</h3>
        <!-- Answer details will be inserted here -->
      </div>
    </div>
  </div>
  <!-- Shared Header and Footer JavaScript -->
  <script src="../../shared/header-footer.js"></script>
</body>
</html>

  <script>
    // Correct answers
    const correctAnswers = {
      q1: "A", // x = 6, y = 4
      q2: "A", // x = 2, y = 1
      q3: "B", // x = 4, y = 3
      q4: "A", // x = 2, y = -1
      q5: "B", // x = 4, y = 1
      q6: "B", // x = 3, y = 3
      q7: "C", // x = 3, y = -1
      q8: "C", // x = 2, y = 4
      q9: "A", // x = 2, y = 1
      q10: "C", // 30 chickens, 20 ducks
      q11: "A", // x = 12, y = 6
      q12: "A", // x = 3, y = 2
      q13: "D", // 4 pencils, 8 erasers
      q14: "B", // x = 3, y = 2
      q15: "C"  // 60 tickets by group 1, 60 tickets by group 2
    };

    // Track answered questions
    let answeredQuestions = 0;
    const totalQuestions = 15;
    
    // Update progress when an option is selected
    const radioButtons = document.querySelectorAll('input[type="radio"]');
    radioButtons.forEach(radio => {
      radio.addEventListener('change', updateProgress);
    });
    
    function updateProgress() {
      // Count answered questions
      answeredQuestions = 0;
      for (let i = 1; i <= totalQuestions; i++) {
        if (document.querySelector(`input[name="q${i}"]:checked`)) {
          answeredQuestions++;
        }
      }
      
      // Update progress text and bar
      document.getElementById('progress-count').textContent = `${answeredQuestions}/${totalQuestions} answered`;
      const progressPercentage = (answeredQuestions / totalQuestions) * 100;
      document.getElementById('progress-fill').style.width = `${progressPercentage}%`;
    }

    // Handle quiz submission
    document.getElementById('submit-btn').addEventListener('click', function() {
      let score = 0;
      let answersHTML = '';
      
      // Check each question
      for (let i = 1; i <= totalQuestions; i++) {
        const questionName = 'q' + i;
        const selectedOption = document.querySelector(`input[name="${questionName}"]:checked`);
        const questionElement = document.querySelector(`input[name="${questionName}"]`).closest('.question');
        const questionText = questionElement.querySelector('.question-text').textContent;
        const mathContent = questionElement.querySelector('.math-content').innerHTML;
        
        // Start answer item HTML
        answersHTML += `<div class="answer-item">`;
        answersHTML += `<p><strong>Question ${i}:</strong></p>`;
        answersHTML += `<div class="math-content">${mathContent}</div>`;
        answersHTML += `<p>${questionText}</p>`;
        
        if (selectedOption) {
          const userAnswer = selectedOption.value;
          const isCorrect = userAnswer === correctAnswers[questionName];
          
          if (isCorrect) {
            score++;
            answersHTML += `<p class="correct">Your answer: ${userAnswer} - Correct!</p>`;
          } else {
            answersHTML += `<p class="incorrect">Your answer: ${userAnswer} - Incorrect</p>`;
            answersHTML += `<p class="correct">Correct answer: ${correctAnswers[questionName]}</p>`;
          }
        } else {
          answersHTML += `<p class="incorrect">Not answered</p>`;
          answersHTML += `<p class="correct">Correct answer: ${correctAnswers[questionName]}</p>`;
        }
        
        answersHTML += `</div>`;
      }
      
      // Update score
      document.getElementById('score').textContent = score;
      
      // Determine feedback based on score
      let feedbackText = '';
      if (score <= 5) {
        feedbackText = "You need more practice with systems of equations. Review the basic methods for solving systems and try again.";
      } else if (score <= 10) {
        feedbackText = "Good effort! You understand the basics of systems of equations, but could use more practice with complex problems.";
      } else if (score <= 14) {
        feedbackText = "Great job! You have a strong understanding of systems of equations.";
      } else {
        feedbackText = "Perfect score! You've mastered systems of equations!";
      }
      
      document.getElementById('feedback').textContent = feedbackText;
      
      // Add answers to the answer key section
      document.getElementById('answer-key').innerHTML = `<h3>Answer Key</h3>${answersHTML}`;
      
      // Hide the quiz form and show results
      document.getElementById('quiz-form').style.display = 'none';
      document.getElementById('results').style.display = 'block';
      
      // Scroll to results
      document.getElementById('results').scrollIntoView({ behavior: 'smooth' });
    });
  </script>
  <!-- Shared Header and Footer JavaScript -->
  <script src="../../shared/header-footer.js"></script>
</body>
</html>
