<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GED Math: Basic Algebra Practice Quiz | Free GED Prep</title>
    <meta name="description" content="Free GED Math practice quiz on basic algebra. Test your skills with 15 questions covering essential algebra concepts for the GED test. Get instant feedback and explanations.">
    <meta name="keywords" content="GED math practice, algebra quiz, basic algebra, GED test prep, free GED practice, GED math questions, algebra equations, GED study guide">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <!-- Shared Header/Footer CSS -->
  <link rel="stylesheet" href="../../shared/header-footer.css">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <!-- Shared Header/Footer CSS -->
  <link rel="stylesheet" href="../../shared/header-footer.css"></noscript>
    
    <!-- Defer non-critical CSS -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js" as="script">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    
    <!-- Add structured data for rich results -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Quiz",
      "name": "GED Math: Basic Algebra Practice Quiz",
      "description": "Free GED Math practice quiz with 15 questions on basic algebra concepts. Perfect for GED test prep with instant scoring and explanations.",
      "educationalAlignment": {
        "@type": "AlignmentObject",
        "alignmentType": "educationalSubject",
        "targetName": "Mathematics"
      },
      "educationalUse": "Practice Quiz",
      "audience": {
        "@type": "EducationalAudience",
        "educationalRole": "student"
      },
      "provider": {
        "@type": "Person",
        "name": "Faruk Hasan",
        "url": "https://faruk-hasan.com"
      }
    }
    </script>
    
    <!-- Inline critical CSS -->
    <style>
        :root {
            --primary-color: #3a6ea5;
            --secondary-color: #004e98;
            --accent-color: #ff6b35;
            --correct-color: #4caf50;
            --incorrect-color: #f44336;
            --neutral-color: #f5f5f5;
            --text-color: #333;
            --light-text: #666;
            --white: #fff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
      /* Body padding/margin removed to match resources.html header layout */
            background-color: #f0f2f5;
            color: var(--text-color);
            line-height: 1.6;
        }

        .quiz-container {
            max-width: 800px;
            margin: 2rem auto;
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .quiz-header {
            background-color: var(--primary-color);
            color: var(--white);
            padding: 1.5rem 2rem;
            text-align: center;
        }

        .quiz-header h1 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }

        .quiz-intro {
            background-color: var(--neutral-color);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            color: var(--text-color);
            width: 100%;
        }

        .quiz-progress {
            height: 6px;
            background-color: rgba(255, 255, 255, 0.3);
            margin-top: 1.5rem;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background-color: var(--accent-color);
            width: 0%;
            transition: width 0.3s ease;
        }

        .quiz-body {
            
        }

        .question-container {
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #eee;
        }

        .question-text {
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 1.5rem;
            color: var(--text-color);
        }

        .question-number {
            display: inline-block;
            background-color: var(--primary-color);
            color: var(--white);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            margin-right: 10px;
        }

        .options-container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .option {
            background-color: var(--neutral-color);
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            display: flex;
            align-items: center;
        }

        .option:hover {
            background-color: #e8e8e8;
        }

        .option.selected {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .option.correct {
            background-color: rgba(76, 175, 80, 0.2);
            border: 1px solid var(--correct-color);
        }

        .option.incorrect {
            background-color: rgba(244, 67, 54, 0.2);
            border: 1px solid var(--incorrect-color);
        }

        .option-letter {
            background-color: var(--primary-color);
            color: var(--white);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }

        .option.selected .option-letter {
            background-color: var(--white);
            color: var(--primary-color);
        }

        .result-icon {
            display: none;
            position: absolute;
            right: 15px;
            font-size: 1.2rem;
        }

        .result-icon.correct {
            color: var(--correct-color);
        }

        .result-icon.incorrect {
            color: var(--incorrect-color);
        }

        .quiz-footer {
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            border-top: 1px solid #eee;
        }

        .quiz-btn {
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            display: flex;
            align-items: center;
            transition: background-color 0.2s;
        }

        .quiz-btn:hover {
            background-color: var(--secondary-color);
        }

        .quiz-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .results-container {
            display: none;
            padding: 2rem;
            text-align: center;
        }

        .results-container.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        .results-score {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin: 1rem 0;
        }

        .results-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: var(--text-color);
        }

        .results-details {
            text-align: left;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #eee;
        }

        .answer-item {
            margin-bottom: 1.5rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #eee;
        }

        .answer-item p {
            margin-bottom: 0.5rem;
        }

        .correct {
            color: var(--correct-color);
        }

        .incorrect {
            color: var(--incorrect-color);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .quiz-container {
                margin: 1rem;
                border-radius: 8px;
            }

            .quiz-header {
                padding: 1.2rem;
            }

            .quiz-header h1 {
                font-size: 1.5rem;
            }

            .quiz-body, .quiz-footer {
                padding: 1.2rem;
            }

            .question-text {
                font-size: 1.1rem;
            }

            .option {
                padding: 0.8rem;
            }
        }
        
        .faq-section, .related-resources {
            width: 100%;
            max-width: 800px;
            margin: 2rem auto;
            padding: 1.5rem;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .faq-section h2, .related-resources h2 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }
        
        .faq-item {
            margin-bottom: 1.5rem;
        }
        
        .faq-item h3 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: var(--secondary-color);
        }
        
        .related-resources ul {
            list-style: none;
            padding: 0;
        }
        
        .related-resources li {
            margin-bottom: 0.8rem;
        }
        
        .related-resources a {
            color: var(--primary-color);
            text-decoration: none;
            display: inline-block;
            padding: 0.3rem 0;
        }
        
        .related-resources a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="quiz-container">
        <div class="quiz-header">
            <h1>GED Math: Basic Algebra Practice Quiz</h1>
            <p>Test your understanding of basic algebra concepts - essential skills for the GED Math test</p>
            
            <!-- Add SEO-friendly introduction -->
            <div class="quiz-intro">
                <p>Welcome to our free GED Math practice quiz on basic algebra. This 15-question quiz helps you prepare for the GED Math test by focusing on essential algebra concepts.</p>
                
                <p>This quiz covers solving linear equations, simplifying expressions, and working with variables - all critical skills for success on the GED Math test.</p>
                
                <p><strong>GED Math Test Info:</strong> Algebra makes up approximately 25-30% of the GED Math section (115 minutes, ~45 questions).</p>
            </div>
            
            <div class="quiz-progress">
                <div class="progress-bar" id="progress-bar"></div>
            </div>
        </div>

        <div class="quiz-body">
            <!-- Question containers remain the same -->
            <div class="question-container" id="question-1">
                <div class="question-text">
                    <span class="question-number">1</span>
                    What is the solution to the equation \(2x + 3 = 11\)?
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">4</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">5</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">6</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">7</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 2 -->
            <div class="question-container" id="question-2">
                <div class="question-text">
                    <span class="question-number">2</span>
                    Simplify the expression: \(4x + 3x - 7\)
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">\(7x - 7\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">\(7x + 7\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">\(5x - 7\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">\(7x\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 3 -->
            <div class="question-container" id="question-3">
                <div class="question-text">
                    <span class="question-number">3</span>
                    What is the value of \(x\) in the equation \(3x - 4 = 11\)?
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">5</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">3</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">7</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">6</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 4 -->
            <div class="question-container" id="question-4">
                <div class="question-text">
                    <span class="question-number">4</span>
                    If \(2x - 3 = 7\), what is the value of \(x\)?
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">5</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">6</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">4</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">3</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 5 -->
            <div class="question-container" id="question-5">
                <div class="question-text">
                    <span class="question-number">5</span>
                    Which of the following is the solution to the equation \(x/5 = 4\)?
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">20</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">15</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">10</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">5</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 6 -->
            <div class="question-container" id="question-6">
                <div class="question-text">
                    <span class="question-number">6</span>
                    Solve the equation: \(5x + 2 = 17\)
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">3</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">4</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">5</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">7</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 7 -->
            <div class="question-container" id="question-7">
                <div class="question-text">
                    <span class="question-number">7</span>
                    Solve for \(x\): \(4x - 8 = 16\)
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">8</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">6</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">4</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">5</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 8 -->
            <div class="question-container" id="question-8">
                <div class="question-text">
                    <span class="question-number">8</span>
                    What is the solution to the equation \(3x + 5 = 20\)?
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">5</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">4</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">6</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">7</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 9 -->
            <div class="question-container" id="question-9">
                <div class="question-text">
                    <span class="question-number">9</span>
                    Solve for \(x\): \(2x + 7 = 15\)
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">3</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">4</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">5</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">6</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 10 -->
            <div class="question-container" id="question-10">
                <div class="question-text">
                    <span class="question-number">10</span>
                    If \(3x + 2 = 14\), what is the value of \(x\)?
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">4</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">5</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">3</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">6</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 11 -->
            <div class="question-container" id="question-11">
                <div class="question-text">
                    <span class="question-number">11</span>
                    Simplify the expression: \(6x + 4x - 9\)
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">\(10x - 9\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">\(10x + 9\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">\(5x - 9\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">\(5x + 9\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 12 -->
            <div class="question-container" id="question-12">
                <div class="question-text">
                    <span class="question-number">12</span>
                    Solve for \(x\): \(2x - 5 = 9\)
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">7</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">6</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">8</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">5</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 13 -->
            <div class="question-container" id="question-13">
                <div class="question-text">
                    <span class="question-number">13</span>
                    What is the solution to the equation \(x - 6 = 10\)?
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">4</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">10</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">16</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">20</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 14 -->
            <div class="question-container" id="question-14">
                <div class="question-text">
                    <span class="question-number">14</span>
                    Simplify the expression: \(2x + 5x - 3\)
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">\(7x - 3\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">\(7x + 3\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">\(3x - 3\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">\(3x + 3\)</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>

            <!-- Question 15 -->
            <div class="question-container" id="question-15">
                <div class="question-text">
                    <span class="question-number">15</span>
                    Solve the equation: \(6x + 8 = 26\)
                </div>
                <div class="options-container">
                    <div class="option" data-option="a">
                        <div class="option-letter">A</div>
                        <div class="option-text">3</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="b">
                        <div class="option-letter">B</div>
                        <div class="option-text">4</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="c">
                        <div class="option-letter">C</div>
                        <div class="option-text">5</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                    <div class="option" data-option="d">
                        <div class="option-letter">D</div>
                        <div class="option-text">6</div>
                        <i class="fas fa-check result-icon correct"></i>
                        <i class="fas fa-times result-icon incorrect"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="quiz-footer">
            <button id="check-answers-btn" class="quiz-btn">Check Answers <i class="fas fa-check-circle"></i></button>
            <button id="reset-quiz-btn" class="quiz-btn">Reset Quiz <i class="fas fa-redo"></i></button>
        </div>
        
        <div id="results-container" class="results-container">
            <h2>Quiz Results</h2>
            <div class="results-score">
                <span id="score">0</span> / <span id="total-questions">15</span>
            </div>
            <div class="results-message" id="results-message">
                Keep practicing to improve your algebra skills!
            </div>
            <div id="results-breakdown" class="results-details"></div>
            <button id="restart-btn" class="quiz-btn restart-btn">
                <i class="fas fa-redo"></i> Try Again
            </button>
        </div>
    </div>
    
    <!-- Add FAQ section with structured data at the bottom -->
    <div class="faq-section">
        <h2>Frequently Asked Questions about Basic Algebra</h2>
        <div class="faq-item">
            <h3>Why is algebra important for the GED test?</h3>
            <p>Algebra is a significant component of the GED Math test, making up about 25-30% of the questions. Understanding algebraic concepts is essential for solving equations, interpreting graphs, and working with formulas - all critical skills for passing the GED Math section.</p>
        </div>
        <div class="faq-item">
            <h3>What algebra topics are covered on the GED?</h3>
            <p>The GED Math test covers solving linear equations and inequalities, graphing, systems of equations, polynomials, and using formulas to solve problems. This quiz focuses on the fundamental skills of solving basic equations and simplifying expressions.</p>
        </div>
        <div class="faq-item">
            <h3>How can I improve my algebra skills?</h3>
            <p>Regular practice is key to improving algebra skills. Start with basic concepts like solving simple equations, then gradually work up to more complex problems. Use online resources, practice quizzes like this one, and consider using a GED prep book or taking a prep course if you need additional support.</p>
        </div>
    </div>
    
    <!-- Add related resources section -->
    <div class="related-resources">
        <h2>More GED Math Practice Resources</h2>
        <ul>
            <li><a href="basic_arithmetic_operations.html">Order of Operations (PEMDAS) Quiz</a></li>
            <li><a href="system_of_equations.html">Systems of Equations Practice</a></li>
            <li><a href="../ged-fractions-decimals.html">Fractions & Decimals Quiz</a></li>
            <li><a href="../ged-geometry.html">Geometry & Measurement Practice</a></li>
            <li><a href="../ged_prep_test_1.html">Complete GED Math Practice Test</a></li>
            <li><a href="../ged-math-quizzes.html">All GED Math Quizzes</a></li>
        </ul>
    </div>

    <script>
        // Correct answers for each question
        const correctAnswers = {
            1: 'a', // 2x + 3 = 11 => x = 4
            2: 'a', // 4x + 3x - 7 = 7x - 7
            3: 'a', // 3x - 4 = 11 => x = 5
            4: 'a', // 2x - 3 = 7 => x = 5
            5: 'a', // x/5 = 4 => x = 20
            6: 'a', // 5x + 2 = 17 => x = 3
            7: 'b', // 4x - 8 = 16 => x = 6
            8: 'a', // 3x + 5 = 20 => x = 5
            9: 'b', // 2x + 7 = 15 => x = 4
            10: 'a', // 3x + 2 = 14 => x = 4
            11: 'a', // 6x + 4x - 9 = 10x - 9
            12: 'a', // 2x - 5 = 9 => x = 7
            13: 'c', // x - 6 = 10 => x = 16
            14: 'a', // 2x + 5x - 3 = 7x - 3
            15: 'a'  // 6x + 8 = 26 => x = 3
        };

        // Feedback messages based on score
        const feedbackMessages = [
            { min: 0, max: 5, message: "You need more practice with basic algebra. Focus on understanding how to solve simple equations." },
            { min: 6, max: 10, message: "You're making progress! Continue practicing to improve your algebra skills." },
            { min: 11, max: 15, message: "Great job! You have a strong understanding of basic algebra concepts." }
        ];

        document.addEventListener('DOMContentLoaded', function() {
            const options = document.querySelectorAll('.option');
            const checkAnswersBtn = document.getElementById('check-answers-btn');
            const resetQuizBtn = document.getElementById('reset-quiz-btn');
            const resultsContainer = document.getElementById('results-container');
            const scoreElement = document.getElementById('score');
            const totalQuestionsElement = document.getElementById('total-questions');
            const resultsBreakdownElement = document.getElementById('results-breakdown');
            const progressBar = document.getElementById('progress-bar');
            
            let selectedAnswers = {};
            let answeredQuestions = 0;
            const totalQuestions = Object.keys(correctAnswers).length;
            
            totalQuestionsElement.textContent = totalQuestions;
            
            // Option selection
            options.forEach(option => {
                option.addEventListener('click', function() {
                    const questionId = parseInt(this.closest('.question-container').id.split('-')[1]);
                    const optionValue = this.getAttribute('data-option');
                    
                    // Remove selected class from all options in this question
                    const questionOptions = this.closest('.options-container').querySelectorAll('.option');
                    questionOptions.forEach(opt => opt.classList.remove('selected'));
                    
                    // Add selected class to clicked option
                    this.classList.add('selected');
                    
                    // Update selected answers
                    if (!selectedAnswers[questionId]) {
                        answeredQuestions++;
                    }
                    selectedAnswers[questionId] = optionValue;
                    
                    // Update progress bar
                    updateProgressBar();
                });
            });
            
            // Update progress bar
            function updateProgressBar() {
                const progress = (answeredQuestions / totalQuestions) * 100;
                progressBar.style.width = `${progress}%`;
            }
            
            // Check answers
            checkAnswersBtn.addEventListener('click', function() {
                let score = 0;
                let resultsHTML = '';
                
                for (let i = 1; i <= totalQuestions; i++) {
                    const questionContainer = document.getElementById(`question-${i}`);
                    const selectedOption = selectedAnswers[i];
                    
                    if (selectedOption) {
                        const optionElement = questionContainer.querySelector(`.option[data-option="${selectedOption}"]`);
                        
                        if (selectedOption === correctAnswers[i]) {
                            score++;
                            optionElement.classList.add('correct');
                            optionElement.querySelector('.correct').style.display = 'block';
                            resultsHTML += `<p>Question ${i}: Correct</p>`;
                        } else {
                            optionElement.classList.add('incorrect');
                            optionElement.querySelector('.incorrect').style.display = 'block';
                            
                            // Highlight correct answer
                            const correctOptionElement = questionContainer.querySelector(`.option[data-option="${correctAnswers[i]}"]`);
                            correctOptionElement.classList.add('correct');
                            correctOptionElement.querySelector('.correct').style.display = 'block';
                            
                            resultsHTML += `<p>Question ${i}: Incorrect - The correct answer is ${correctAnswers[i].toUpperCase()}</p>`;
                        }
                    } else {
                        // Highlight correct answer for unanswered questions
                        const correctOptionElement = questionContainer.querySelector(`.option[data-option="${correctAnswers[i]}"]`);
                        correctOptionElement.classList.add('correct');
                        correctOptionElement.querySelector('.correct').style.display = 'block';
                        
                        resultsHTML += `<p>Question ${i}: Not answered - The correct answer is ${correctAnswers[i].toUpperCase()}</p>`;
                    }
                }
                
                scoreElement.textContent = score;
                resultsBreakdownElement.innerHTML = resultsHTML;
                resultsContainer.style.display = 'block';
                
                // Disable option selection after checking answers
                options.forEach(option => {
                    option.style.pointerEvents = 'none';
                });
                
                // Disable check answers button
                this.disabled = true;

                // Display feedback message
                const feedbackMessage = feedbackMessages.find(msg => score >= msg.min && score <= msg.max);
                if (feedbackMessage) {
                    document.getElementById('results-message').textContent = feedbackMessage.message;
                }
            });
            
            // Reset quiz
            resetQuizBtn.addEventListener('click', function() {
                // Clear selected answers
                selectedAnswers = {};
                answeredQuestions = 0;
                
                // Reset UI
                options.forEach(option => {
                    option.classList.remove('selected', 'correct', 'incorrect');
                    option.style.pointerEvents = 'auto';
                    option.querySelector('.correct').style.display = 'none';
                    option.querySelector('.incorrect').style.display = 'none';
                });
                
                // Reset progress bar
                progressBar.style.width = '0%';
                
                // Hide results
                resultsContainer.style.display = 'none';
                
                // Enable check answers button
                checkAnswersBtn.disabled = false;
            });
        });
    </script>
  <!-- Shared Header and Footer JavaScript -->
  <script src="../../shared/header-footer.js"></script>
</body>
</html>
