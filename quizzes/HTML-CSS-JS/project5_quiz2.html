<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>JavaScript Arrays & Functions Quiz</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Shared Header/Footer CSS -->
  <link rel="stylesheet" href="../../shared/header-footer.css">
  <style>
    :root {
      --primary-color: #0077b5;
      --primary-dark: #005885;
      --secondary-color: #f5f7fa;
      --text-color: #333;
      --light-gray: #f5f5f5;
      --border-color: #e0e0e0;
      --success-color: #27ae60;
      --error-color: #e74c3c;
      --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    body {
      /* Body padding/margin removed to match resources.html header layout */
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      
      
      background-color: var(--secondary-color);
      color: var(--text-color);
    }
    
    h1 {
      text-align: center;
      color: var(--primary-color);
      margin-bottom: 30px;
      font-size: 2.2rem;
      border-bottom: 2px solid var(--border-color);
      padding-bottom: 10px;
    }
    
    .quiz-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 10px;
      box-shadow: var(--box-shadow);
      padding: 25px;
    }
    
    .quiz-header {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid var(--border-color);
    }
    
    .quiz-header h1 {
      color: var(--primary-color);
      margin-bottom: 10px;
      border-bottom: none;
      padding-bottom: 0;
    }
    
    .quiz-header p {
      color: #666;
      font-size: 1.1rem;
    }
    
    .progress-container {
      margin-bottom: 30px;
      background: #f0f3f7;
      height: 10px;
      border-radius: 5px;
      overflow: hidden;
      position: sticky;
      top: 0;
      padding: 10px 0;
      z-index: 100;
      background-color: white;
    }
    
    #progress-fill {
      height: 100%;
      background: var(--success-color);
      width: 0;
      transition: width 0.3s ease;
    }
    
    #progress-count {
      text-align: right;
      font-size: 0.9rem;
      color: #666;
      margin-top: 5px;
      position: sticky;
      top: 30px;
      z-index: 100;
      background-color: white;
      padding-bottom: 10px;
    }
    
    /* Category styling for questions */
    .question {
      margin-bottom: 25px;
      padding: 20px;
      background: #eef2f5;
      border-left: 4px solid var(--primary-color);
      border-radius: 5px;
      transition: transform 0.2s ease, box-shadow 0.3s ease;
    }

    /* Different category colors */
    .question.javascript {
      border-left-color: #f7df1e; /* JavaScript yellow */
    }

    .question.arrays {
      border-left-color: #e44d26; /* Arrays orange-red */
    }

    .question.functions {
      border-left-color: #2965f1; /* Functions blue */
    }

    .question.loops {
      border-left-color: #7248b6; /* Loops purple */
    }

    .question.methods {
      border-left-color: #27ae60; /* Methods green */
    }

    /* Category badges */
    .category-badge {
      display: inline-block;
      font-size: 0.75rem;
      padding: 3px 8px;
      border-radius: 12px;
      margin-bottom: 8px;
      font-weight: 500;
      color: white;
    }

    .category-badge.javascript {
      background-color: #f7df1e;
      color: #333;
    }

    .category-badge.arrays {
      background-color: #e44d26;
    }

    .category-badge.functions {
      background-color: #2965f1;
    }

    .category-badge.loops {
      background-color: #7248b6;
    }

    .category-badge.methods {
      background-color: #27ae60;
    }
    
    .question:hover {
      transform: translateY(-2px);
      box-shadow: 0 3px 10px rgba(0,0,0,0.05);
    }
    
    .question h3 {
      margin-top: 0;
      font-size: 1.2rem;
      display: flex;
      align-items: center;
    }
    
    .question-number {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      margin-right: 10px;
      font-size: 0.9rem;
      flex-shrink: 0;
    }
    
    pre {
      background-color: #f8f9fa;
      padding: 12px;
      border-radius: 5px;
      border-left: 3px solid #ddd;
      font-family: Consolas, Monaco, 'Andale Mono', monospace;
      font-size: 0.9rem;
      overflow-x: auto;
      margin: 15px 0;
    }
    
    .answers {
      margin-left: 20px;
    }
    
    .answers label {
      display: block;
      padding: 10px 15px;
      margin-bottom: 8px;
      background-color: #f8f9fa;
      border-radius: 5px;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .answers label:hover {
      background-color: #e9ecef;
    }
    
    .answers input[type="radio"] {
      margin-right: 10px;
    }
    
    .submit-btn {
      display: block;
      margin: 30px auto 20px;
      padding: 12px 25px;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 5px;
      font-size: 1rem;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .submit-btn:hover {
      background-color: var(--primary-dark);
    }
    
    #result {
      margin-top: 30px;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 8px;
      text-align: center;
      display: none;
    }
    
    .score-container {
      margin-bottom: 20px;
    }
    
    .score-circle {
      width: 120px;
      height: 120px;
      background: var(--success-color);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2.5rem;
      font-weight: bold;
      margin: 0 auto 20px;
      box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
    }
    
    .result-feedback {
      font-size: 1.2rem;
      margin-bottom: 20px;
      padding: 15px;
      background: #e0f7e9;
      border-radius: 5px;
      color: #2c7a51;
    }
    
    .answer-key {
      text-align: left;
      margin-top: 30px;
    }
    
    .answer-key h3 {
      color: var(--primary-color);
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 10px;
      margin-bottom: 20px;
    }
    
    .answer-item {
      margin-bottom: 20px;
      padding: 15px;
      background: white;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }
    
    .correct {
      color: var(--success-color);
      font-weight: bold;
    }
    
    .incorrect {
      color: var(--error-color);
      font-weight: bold;
    }
    
    .explanation {
      margin-top: 10px;
      padding: 10px;
      background: #f5f5f5;
      border-left: 3px solid #ddd;
      font-style: italic;
    }
    
    .back-to-resources {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 10px 20px;
      background: #f0f3f7;
      color: var(--primary-color);
      text-decoration: none;
      border-radius: 5px;
      transition: all 0.2s ease;
      margin-top: 20px;
    }
    
    .back-to-resources:hover {
      background: #e0e5eb;
    }
    
    @media (max-width: 768px) {
      .quiz-container {
        padding: 15px;
      }
      
      h1 {
        font-size: 1.8rem;
      }
      
      .question {
        padding: 15px;
      }
      
      .answers label {
        padding: 8px 12px;
      }
    }
  </style>
</head>
<body>

  <div class="quiz-container">
    <div class="quiz-header">
      <h1>JavaScript Quiz: Loops and Functions</h1>
      <p>Test your knowledge of JavaScript arrays, loops, and functions with this interactive quiz.</p>
    </div>
    
    <div class="progress-container">
      <div id="progress-fill"></div>
    </div>
    <div id="progress-count">0/10 answered</div>

    <form id="quizForm">
      <div class="question arrays">
        <span class="category-badge arrays">Arrays</span>
        <h3><span class="question-number">1</span> What will be the output of the following code?</h3>
        <pre>
let sum = 0;
for (let i = 1; i <= 5; i++) {
    sum += i;
}
console.log(sum);
        </pre>
        <div class="answers">
          <label><input type="radio" name="q1" value="A"> A) 10</label>
          <label><input type="radio" name="q1" value="B"> B) 15</label>
          <label><input type="radio" name="q1" value="C"> C) 5</label>
          <label><input type="radio" name="q1" value="D"> D) 20</label>
        </div>
      </div>

      <div class="question loops">
        <span class="category-badge loops">Loops</span>
        <h3><span class="question-number">2</span> What is the output of the following while loop?</h3>
        <pre>
let i = 0;
while (i < 3) {
    console.log(i);
    i++;
}
        </pre>
        <div class="answers">
          <label><input type="radio" name="q2" value="A"> A) 0 1 2</label>
          <label><input type="radio" name="q2" value="B"> B) 0 1</label>
          <label><input type="radio" name="q2" value="C"> C) 1 2 3</label>
          <label><input type="radio" name="q2" value="D"> D) 1 2</label>
        </div>
      </div>

      <div class="question loops">
        <span class="category-badge loops">Loops</span>
        <h3><span class="question-number">3</span> What will the output of the following code be?</h3>
        <pre>
let i = 0;
for (i = 0; i < 3; i++) {
    console.log(i);
    while (i == 1) {
        console.log("Inside while loop");
        break;
    }
}
        </pre>
        <div class="answers">
          <label><input type="radio" name="q3" value="A"> A) 0 1 Inside while loop 2</label>
          <label><input type="radio" name="q3" value="B"> B) 0 1 2 Inside while loop</label>
          <label><input type="radio" name="q3" value="C"> C) 0 1 2</label>
          <label><input type="radio" name="q3" value="D"> D) 0 Inside while loop 2</label>
        </div>
      </div>

      <div class="question methods">
        <span class="category-badge methods">Methods</span>
        <h3><span class="question-number">4</span> What is the output of the following code?</h3>
        <pre>
let str = "apple,banana,cherry";
let arr = str.split(",");
console.log(arr);
        </pre>
        <div class="answers">
          <label><input type="radio" name="q4" value="A"> A) ["apple", "banana", "cherry"]</label>
          <label><input type="radio" name="q4" value="B"> B) ["apple banana cherry"]</label>
          <label><input type="radio" name="q4" value="C"> C) ["apple,banana,cherry"]</label>
          <label><input type="radio" name="q4" value="D"> D) ["apple", "banana", "cherry", ""]</label>
        </div>
      </div>

      <div class="question methods">
        <span class="category-badge methods">Methods</span>
        <h3><span class="question-number">5</span> Which of the following will join the array ["apple", "banana", "cherry"] into a string "apple-banana-cherry"?</h3>
        <div class="answers">
          <label><input type="radio" name="q5" value="A"> A) arr.join(" ") </label>
          <label><input type="radio" name="q5" value="B"> B) arr.join("-") </label>
          <label><input type="radio" name="q5" value="C"> C) arr.split("-") </label>
          <label><input type="radio" name="q5" value="D"> D) arr.join(",") </label>
        </div>
      </div>

      <div class="question arrays">
        <span class="category-badge arrays">Arrays</span>
        <h3><span class="question-number">6</span> What will be the result of the following code?</h3>
        <pre>
let arr = [1, 2, 3, 4, 5];
arr[3] = 10;
console.log(arr);
        </pre>
        <div class="answers">
          <label><input type="radio" name="q6" value="A"> A) [1, 2, 3, 10, 5]</label>
          <label><input type="radio" name="q6" value="B"> B) [1, 2, 3, 4, 5, 10]</label>
          <label><input type="radio" name="q6" value="C"> C) [1, 2, 3, 4, 5, 6]</label>
          <label><input type="radio" name="q6" value="D"> D) [10, 2, 3, 4, 5]</label>
        </div>
      </div>

      <div class="question functions">
        <span class="category-badge functions">Functions</span>
        <h3><span class="question-number">7</span> Which of the following correctly defines a function named "greet" that logs "Hello, World!"?</h3>
        <div class="answers">
          <label><input type="radio" name="q7" value="A"> A) function greet() { console.log("Hello, World!"); }</label>
          <label><input type="radio" name="q7" value="B"> B) function greet() = { console.log("Hello, World!"); }</label>
          <label><input type="radio" name="q7" value="C"> C) function greet { console.log("Hello, World!"); }</label>
          <label><input type="radio" name="q7" value="D"> D) function greet(): { console.log("Hello, World!"); }</label>
        </div>
      </div>

      <div class="question functions">
        <span class="category-badge functions">Functions</span>
        <h3><span class="question-number">8</span> Which of the following is the correct way to call a function named "initialize()" when the page loads?</h3>
        <div class="answers">
          <label><input type="radio" name="q8" value="A"> A) &lt;body onload="initialize()"&gt; </label>
          <label><input type="radio" name="q8" value="B"> B) window.onload = initialize(); </label>
          <label><input type="radio" name="q8" value="C"> C) initialize() </label>
          <label><input type="radio" name="q8" value="D"> D) &lt;body onload="initialize()"&gt; </label>
        </div>
      </div>

      <div class="question loops">
        <span class="category-badge loops">Loops</span>
        <h3><span class="question-number">9</span> The following code produces an infinite loop. What is wrong with it?</h3>
        <pre>
let i = 0;
for (i = 0; i < 10;) {
    console.log(i);
}
        </pre>
        <div class="answers">
          <label><input type="radio" name="q9" value="A"> A) The loop condition is incorrect. </label>
          <label><input type="radio" name="q9" value="B"> B) The increment part is missing in the loop. </label>
          <label><input type="radio" name="q9" value="C"> C) The variable i is not defined correctly. </label>
          <label><input type="radio" name="q9" value="D"> D) The loop needs a break statement. </label>
        </div>
      </div>

      <div class="question methods">
        <span class="category-badge methods">Methods</span>
        <h3><span class="question-number">10</span> The following code doesn't produce the correct result. What is wrong?</h3>
        <pre>
let text = "apple-banana-cherry";
let result = text.split("-");
console.log(result);
        </pre>
        <div class="answers">
          <label><input type="radio" name="q10" value="A"> A) The separator is incorrect; it should be ",". </label>
          <label><input type="radio" name="q10" value="B"> B) split() method is used incorrectly. </label>
          <label><input type="radio" name="q10" value="C"> C) The separator should be a space " ". </label>
          <label><input type="radio" name="q10" value="D"> D) The split() method will not work with dashes. </label>
        </div>
      </div>

      <button type="button" class="submit-btn" onclick="submitQuiz()">
        <i class="fas fa-check-circle"></i> Submit Quiz
      </button>
    </form>

    <div id="result">
      <div class="score-container">
        <div class="score-circle">0</div>
      </div>
      <div class="result-feedback" id="result-feedback"></div>
      <div class="answer-key">
        <h3>Answer Key</h3>
        <div id="answers"></div>
      </div>
      <div style="text-align: center; margin-top: 30px;">
        <a href="../../resources.html" class="back-to-resources">
          <i class="fas fa-arrow-left"></i> Back to Learning Resources
        </a>
      </div>
    </div>
  </div>

  <script>
    // Track answered questions
    let answeredQuestions = 0;
    const totalQuestions = 10;
    
    // Update progress when a radio button is clicked
    document.querySelectorAll('input[type="radio"]').forEach(radio => {
      radio.addEventListener('change', function() {
        const questionName = this.name;
        const questionGroup = document.querySelectorAll(`input[name="${questionName}"]`);
        let wasAlreadyAnswered = false;
        
        questionGroup.forEach(radio => {
          if (radio.hasAttribute('data-answered')) {
            wasAlreadyAnswered = true;
          }
          radio.setAttribute('data-answered', 'true');
        });
        
        if (!wasAlreadyAnswered) {
          answeredQuestions++;
          updateProgress();
        }
      });
    });
    
    // Update progress bar
    function updateProgress() {
      const progressFill = document.getElementById('progress-fill');
      const progressCount = document.getElementById('progress-count');
      const percentage = (answeredQuestions / totalQuestions) * 100;
      
      progressFill.style.width = `${percentage}%`;
      progressCount.textContent = `${answeredQuestions}/${totalQuestions} answered`;
    }
    
    function submitQuiz() {
      const form = document.getElementById("quizForm");
      const resultDiv = document.getElementById("result");
      const answersDiv = document.getElementById("answers");
      let score = 0;
      let answersHTML = '';

      const answers = {
        q1: 'B',
        q2: 'A',
        q3: 'A',
        q4: 'A',
        q5: 'B',
        q6: 'A',
        q7: 'A',
        q8: 'A',
        q9: 'B',
        q10: 'A',
      };
      
      const explanations = {
        q1: "The loop adds numbers 1 through 5 (1+2+3+4+5), resulting in 15.",
        q2: "The while loop prints 0, then 1, then 2, and stops when i becomes 3.",
        q3: "The code outputs 0, then 1, then 'Inside while loop' (when i is 1), then 2.",
        q4: "The split() method divides a string into an array of substrings using the specified separator.",
        q5: "The join() method creates a string by concatenating array elements with the specified separator.",
        q6: "Array indices start at 0, so arr[3] is the fourth element (value 4), which gets replaced with 10.",
        q7: "The correct syntax for defining a function in JavaScript uses parentheses after the function name and curly braces for the body.",
        q8: "The onload attribute in the body tag is one way to call a function when the page loads.",
        q9: "The for loop is missing the increment part (i++), so i remains 0 and the condition i < 10 is always true.",
        q10: "The code actually works correctly. The split() method with '-' as separator will produce ['apple', 'banana', 'cherry']."
      };

      // Check answers and build result HTML
      for (const [key, value] of Object.entries(answers)) {
        const selectedOption = form.querySelector(`input[name="${key}"]:checked`);
        const isCorrect = selectedOption && selectedOption.value === value;
        
        if (isCorrect) {
          score++;
        }
        
        // Get question text
        const questionElement = form.querySelector(`[name="${key}"]`).closest('.question');
        const questionText = questionElement.querySelector('h3').textContent.replace(/^\d+\s*/, '').trim();
        
        // Get code snippet if it exists
        let codeSnippet = '';
        const preElement = questionElement.querySelector('pre');
        if (preElement) {
          codeSnippet = `<pre>${preElement.textContent.trim()}</pre>`;
        }
        
        // Get user's answer
        const userAnswer = selectedOption ? selectedOption.value : 'No answer';
        
        // Add to answers HTML
        answersHTML += `
          <div class="answer-item">
            <p><strong>Question ${key.replace('q', '')}:</strong> ${questionText}</p>
            ${codeSnippet}
            <p class="${isCorrect ? 'correct' : 'incorrect'}">
              Your answer: ${userAnswer} - ${isCorrect ? 'Correct' : 'Incorrect'}
            </p>
            <p><strong>Correct answer:</strong> ${answers[key]}</p>
            <div class="explanation">${explanations[key]}</div>
          </div>
        `;
      }

      // Feedback messages based on score
      const feedbackMessages = [
        {min: 0, max: 3, message: "Keep studying! Review JavaScript basics, especially loops, arrays, and functions."},
        {min: 4, max: 6, message: "Good effort! You have a basic understanding of JavaScript concepts, but there's room to grow."},
        {min: 7, max: 9, message: "Great job! You have a solid understanding of JavaScript loops, arrays, and functions."},
        {min: 10, max: 10, message: "Perfect score! You've mastered JavaScript loops, arrays, and functions!"}
      ];

      // Find appropriate feedback
      let feedbackMessage = '';
      for (const feedback of feedbackMessages) {
        if (score >= feedback.min && score <= feedback.max) {
          feedbackMessage = feedback.message;
          break;
        }
      }

      // Display results
      document.querySelector('.score-circle').textContent = score;
      document.getElementById('result-feedback').textContent = feedbackMessage;
      document.getElementById('answers').innerHTML = answersHTML;
      
      // Show results
      resultDiv.style.display = 'block';
      resultDiv.scrollIntoView({behavior: 'smooth'});
    }
  </script>

  <!-- Shared Header and Footer JavaScript -->
  <script src="../../shared/header-footer.js"></script>
</body>
</html>
