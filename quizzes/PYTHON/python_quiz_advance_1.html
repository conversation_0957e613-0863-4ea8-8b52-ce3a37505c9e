<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Python Advanced Quiz 1</title>
  <style>
    /* Base styles */
    body {
      /* Body padding/margin removed to match resources.html header layout */
      font-family: Arial, sans-serif;
      background: #f4f7f8;
      
      
      display: flex;
      justify-content: center;
      align-items: flex-start;
      min-height: 100vh;
    }
    .container {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      max-width: 800px;
      width: 100%;
      margin: 2rem;
      padding: 2rem;
    }
    h1 {
      text-align: center;
      margin-bottom: 1.5rem;
      color: #333;
    }
    .question {
      margin-bottom: 1.5rem;
    }
    label {
      display: block;
      font-weight: bold;
      margin-bottom: 0.5rem;
      color: #555;
    }
    input[type="text"] {
      width: 100%;
      padding: 0.5rem;
      font-size: 1rem;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-sizing: border-box;
      transition: border-color 0.2s;
    }
    input[type="text"]:focus {
      border-color: #66afe9;
      outline: none;
    }
    button {
      background: #28a745;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      border-radius: 4px;
      cursor: pointer;
      display: block;
      margin: 1rem auto;
      transition: background 0.2s;
    }
    button:hover {
      background: #218838;
    }
    #results {
      margin-top: 2rem;
      padding: 1rem;
      background: #e9ecef;
      border-radius: 4px;
    }
    #results h2 {
      margin-top: 0;
      color: #333;
    }
    #results ul {
      list-style: none;
      padding: 0;
    }
    #results li {
      padding: 0.5rem 0;
      border-bottom: 1px solid #ddd;
    }
    #results li:last-child {
      border-bottom: none;
    }
  </style>
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Shared Header/Footer CSS -->
  <link rel="stylesheet" href="../../shared/header-footer.css">

</head>
<body>
  <div class="container">
    <h1>Python Basics Quiz</h1>
    <form id="quizForm">
      <div class="question">
        <label for="q1">1. What will be printed when the user enters <code>30</code> in:<br>
          <code>age = input("How old are you? ")<br>print(type(age))</code>?</label>
        <input type="text" id="q1" name="q1" />
      </div>
      <div class="question">
        <label for="q2">2. If the user types <code>7</code> and <code>5</code> in:<br>
          <code>num1 = int(input("Enter first number: "))<br>num2 = int(input("Enter second number: "))<br>print(num1 + num2)</code><br>
          what is the output? What happens if you omit <code>int()</code>?</label>
        <input type="text" id="q2" name="q2" />
      </div>
      <div class="question">
        <label for="q3">3. What does this print?<br>
          <code>fruits = ["apple", "banana", "cherry", "date", "elderberry"]<br>print(fruits[1:4])</code></label>
        <input type="text" id="q3" name="q3" />
      </div>
      <div class="question">
        <label for="q4">4. Which element and value is accessed by:<br>
          <code>nums = [10, 20, 30, 40, 50]<br>print(nums[-3])</code>?</label>
        <input type="text" id="q4" name="q4" />
      </div>
      <div class="question">
        <label for="q5">5. What happens when you run:<br>
          <code>t = (1, 2, 3)<br>t[0] = 10</code>?</label>
        <input type="text" id="q5" name="q5" />
      </div>
      <div class="question">
        <label for="q6">6. After this code:<br>
          <code>student = {"name": "Alice", "score": 85}<br>student["score"] += 5<br>print(student)</code><br>
          what is the resulting dictionary?</label>
        <input type="text" id="q6" name="q6" />
      </div>
      <div class="question">
        <label for="q7">7. What is the type and contents of <code>colors</code>?<br>
          <code>text = "red,green,blue,yellow"<br>colors = text.split(",")</code></label>
        <input type="text" id="q7" name="q7" />
      </div>
      <div class="question">
        <label for="q8">8. What is the value of <code>sentence</code>?<br>
          <code>words = ["hello", "world"]<br>sentence = " ".join(words)</code></label>
        <input type="text" id="q8" name="q8" />
      </div>
      <div class="question">
        <label for="q9">9. Show exactly what prints:<br>
          <code>title = "Chapter 1"<br>print(title.center(20, "-"))</code></label>
        <input type="text" id="q9" name="q9" />
      </div>
      <div class="question">
        <label for="q10">10. What substring is printed?<br>
          <code>s = "abcdefghijklmnopqrstuvwxyz"<br>print(s[5:15:2])</code></label>
        <input type="text" id="q10" name="q10" />
      </div>
      <div class="question">
        <label for="q11">11. What is the output and element types?<br>
          <code>nums_str = ["1", "2", "3", "4"]<br>nums = list(map(int, nums_str))<br>print(nums)</code></label>
        <input type="text" id="q11" name="q11" />
      </div>
      <div class="question">
        <label for="q12">12. What does <code>codes</code> contain?<br>
          <code>letters = ['a', 'b', 'c']<br>codes = list(map(lambda x: ord(x), letters))</code></label>
        <input type="text" id="q12" name="q12" />
      </div>
      <div class="question">
        <label for="q13">13. What is printed?<br>
          <code>data = {"names": ["Alice", "Bob"], "scores": (90, 85)}<br>print(data["names"][1], data["scores"][0])</code></label>
        <input type="text" id="q13" name="q13" />
      </div>
      <div class="question">
        <label for="q14">14. If the user types <code>pen paper eraser</code> in:<br>
          <code>items = input("Enter items separated by space: ").split()<br>print(items)</code><br>
          what does <code>items</code> become?</label>
        <input type="text" id="q14" name="q14" />
      </div>
      <div class="question">
        <label for="q15">15. After running:<br>
          <code>raw = "  apple;banana; cherry  "<br>cleaned = ";".join(raw.strip().split(";"))</code><br>
          what is the exact value of <code>cleaned</code>?</label>
        <input type="text" id="q15" name="q15" />
      </div>
      <button type="submit">Submit Answers</button>
    </form>
    <div id="results" style="display:none;">
      <h2>Your Answers</h2>
      <ul id="answersList"></ul>
    </div>
  </div>
  <script>
    document.getElementById('quizForm').addEventListener('submit', function(e) {
      e.preventDefault();
      const resultsDiv = document.getElementById('results');
      const answersList = document.getElementById('answersList');
      answersList.innerHTML = '';
      for (let i = 1; i <= 15; i++) {
        const answer = document.getElementById('q' + i).value.trim();
        const li = document.createElement('li');
        li.textContent = `Q${i}: ${answer || '(no answer provided)'}`;
        answersList.appendChild(li);
      }
      resultsDiv.style.display = 'block';
      resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
  </script>
  <!-- Shared Header and Footer JavaScript -->
  <script src="../../shared/header-footer.js"></script>
</body>
</html>
