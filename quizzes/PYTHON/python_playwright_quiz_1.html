<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Playwright Python Quiz</title>
  <style>
    body {
      /* Body padding/margin removed to match resources.html header layout */
      font-family: Arial, sans-serif;
      background: #f2f2f2;
      
      max-width: 800px;
      
    }
    h1 {
      text-align: center;
    }
    .question {
      background: #fff;
      padding: 15px;
      margin-bottom: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
    .question h3 {
      margin-top: 0;
    }
    .feedback {
      font-weight: bold;
    }
    button {
      display: block;
      margin: 20px auto;
      padding: 10px 20px;
      font-size: 16px;
      border-radius: 5px;
      border: none;
      background-color: #4CAF50;
      color: white;
      cursor: pointer;
    }
    button:hover {
      background-color: #45a049;
    }
  </style>
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Shared Header/Footer CSS -->
  <link rel="stylesheet" href="../../shared/header-footer.css">

</head>
<body>

  <h1>🎯 Playwright + Python Concepts Quiz</h1>

  <div class="question" id="q1">
    <h3>1. What is the purpose of the <code>sync_playwright()</code> function?</h3>
    <form>
      <label><input type="radio" name="q1" value="C" /> C) Provides a synchronous context to run Playwright operations</label><br>
      <label><input type="radio" name="q1" value="B" /> B) It launches the Playwright server</label><br>
      <label><input type="radio" name="q1" value="A" /> A) It opens a web browser</label><br>
      <label><input type="radio" name="q1" value="D" /> D) Installs Playwright dependencies</label>
    </form>
    <p class="feedback" id="feedback-q1"></p>
  </div>

  <div class="question" id="q2">
    <h3>2. What does <code>headless=False</code> do when launching the browser?</h3>
    <form>
      <label><input type="radio" name="q2" value="B" /> B) Displays the browser UI during execution</label><br>
      <label><input type="radio" name="q2" value="A" /> A) Runs the browser in background without UI</label><br>
      <label><input type="radio" name="q2" value="C" /> C) Disables incognito mode</label><br>
      <label><input type="radio" name="q2" value="D" /> D) Randomizes browser settings</label>
    </form>
    <p class="feedback" id="feedback-q2"></p>
  </div>

  <div class="question" id="q3">
    <h3>3. What is the role of <code>slow_mo=random.randint(50, 150)</code>?</h3>
    <form>
      <label><input type="radio" name="q3" value="A" /> A) Adds delay between Playwright API calls</label><br>
      <label><input type="radio" name="q3" value="B" /> B) Slows down browser loading time</label><br>
      <label><input type="radio" name="q3" value="C" /> C) Limits browser CPU usage</label><br>
      <label><input type="radio" name="q3" value="D" /> D) Delays system boot time</label>
    </form>
    <p class="feedback" id="feedback-q3"></p>
  </div>

  <div class="question" id="q4">
    <h3>4. What does the <code>random_sleep()</code> function do?</h3>
    <form>
      <label><input type="radio" name="q4" value="B" /> B) Pauses execution for a random duration between <code>min_time</code> and <code>max_time</code></label><br>
      <label><input type="radio" name="q4" value="A" /> A) Pauses execution for a fixed time</label><br>
      <label><input type="radio" name="q4" value="C" /> C) Pauses only if there's an error</label><br>
      <label><input type="radio" name="q4" value="D" /> D) Pauses browser rendering</label>
    </form>
    <p class="feedback" id="feedback-q4"></p>
  </div>

  <div class="question" id="q5">
    <h3>5. Which module is used for generating random numbers in this script?</h3>
    <form>
      <label><input type="radio" name="q5" value="C" /> C) random</label><br>
      <label><input type="radio" name="q5" value="A" /> A) time</label><br>
      <label><input type="radio" name="q5" value="B" /> B) playwright</label><br>
      <label><input type="radio" name="q5" value="D" /> D) math</label>
    </form>
    <p class="feedback" id="feedback-q5"></p>
  </div>

  <div class="question" id="q6">
    <h3>6. What does <code>page.locator(selector)</code> do?</h3>
    <form>
      <label><input type="radio" name="q6" value="B" /> B) Finds and returns a reference to a DOM element</label><br>
      <label><input type="radio" name="q6" value="A" /> A) Clicks on a web element</label><br>
      <label><input type="radio" name="q6" value="C" /> C) Takes a screenshot</label><br>
      <label><input type="radio" name="q6" value="D" /> D) Switches browser tabs</label>
    </form>
    <p class="feedback" id="feedback-q6"></p>
  </div>

  <div class="question" id="q7">
    <h3>7. What happens when <code>from_field.fill("LAX")</code> is executed?</h3>
    <form>
      <label><input type="radio" name="q7" value="C" /> C) The input field is filled with the value "LAX"</label><br>
      <label><input type="radio" name="q7" value="A" /> A) The input field is clicked</label><br>
      <label><input type="radio" name="q7" value="B" /> B) The browser navigates to LAX</label><br>
      <label><input type="radio" name="q7" value="D" /> D) A new tab is opened with a search result</label>
    </form>
    <p class="feedback" id="feedback-q7"></p>
  </div>

  <div class="question" id="q8">
    <h3>8. Why is the <code>with sync_playwright() as p:</code> pattern used?</h3>
    <form>
      <label><input type="radio" name="q8" value="C" /> C) Automatically manages resource cleanup after execution</label><br>
      <label><input type="radio" name="q8" value="A" /> A) It's cleaner code style</label><br>
      <label><input type="radio" name="q8" value="B" /> B) It avoids using a browser</label><br>
      <label><input type="radio" name="q8" value="D" /> D) Allows launching multiple browsers</label>
    </form>
    <p class="feedback" id="feedback-q8"></p>
  </div>

  <button onclick="checkAnswers()">Submit Answers</button>

  <script>
    const answers = {
      q1: "C",
      q2: "B",
      q3: "A",
      q4: "B",
      q5: "C",
      q6: "B",
      q7: "C",
      q8: "C"
    };

    function checkAnswers() {
      for (const [questionId, correct] of Object.entries(answers)) {
        const radios = document.querySelectorAll(`input[name="${questionId}"]`);
        let selected = null;
        radios.forEach(r => {
          if (r.checked) selected = r.value;
        });

        const feedback = document.getElementById(`feedback-${questionId}`);
        if (selected === correct) {
          feedback.textContent = "✅ Correct!";
          feedback.style.color = "green";
        } else {
          feedback.textContent = `❌ Incorrect. Correct answer: ${correct}`;
          feedback.style.color = "red";
        }
      }
    }
  </script>

  <!-- Shared Header and Footer JavaScript -->
  <script src="../../shared/header-footer.js"></script>
</body>
</html>
