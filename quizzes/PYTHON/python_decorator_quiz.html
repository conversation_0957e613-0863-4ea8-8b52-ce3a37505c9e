<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Python Decorators Quiz</title>
  <style>
    body {
      /* Body padding/margin removed to match resources.html header layout */
      font-family: Arial, sans-serif;
      background: #f2f2f2;
      
      max-width: 800px;
      
    }
    h1 {
      text-align: center;
    }
    .question {
      background: #fff;
      padding: 15px;
      margin-bottom: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }
    .question h3 {
      margin-top: 0;
    }
    .feedback {
      font-weight: bold;
    }
    button {
      display: block;
      margin: 20px auto;
      padding: 10px 20px;
      font-size: 16px;
      border-radius: 5px;
      border: none;
      background-color: #4CAF50;
      color: white;
      cursor: pointer;
    }
    button:hover {
      background-color: #45a049;
    }
  </style>
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Shared Header/Footer CSS -->
  <link rel="stylesheet" href="../../shared/header-footer.css">

</head>
<body>

  <h1>🐍 Python Functions & Decorators Quiz</h1>

  <div class="question" id="q1">
    <h3>1. What is a class in Python?</h3>
    <form>
      <label><input type="radio" name="q1" value="A" /> A) A blueprint for creating objects</label><br>
      <label><input type="radio" name="q1" value="B" /> B) A built-in data type for storing multiple values</label><br>
      <label><input type="radio" name="q1" value="C" /> C) A special kind of function</label><br>
      <label><input type="radio" name="q1" value="D" /> D) A syntax for looping over items</label>
    </form>
    <p class="feedback" id="feedback-q1"></p>
  </div>

  <div class="question" id="q2">
    <h3>2. Which statement best describes a Python object?</h3>
    <form>
      <label><input type="radio" name="q2" value="A" /> A) An instance created from a class</label><br>
      <label><input type="radio" name="q2" value="B" /> B) A collection of unrelated variables</label><br>
      <label><input type="radio" name="q2" value="C" /> C) A keyword used to define functions</label><br>
      <label><input type="radio" name="q2" value="D" /> D) A file containing Python code</label>
    </form>
    <p class="feedback" id="feedback-q2"></p>
  </div>

  <div class="question" id="q3">
    <h3>3. How would you define a function in Python?</h3>
    <form>
      <label><input type="radio" name="q3" value="A" /> A) A named block of reusable code</label><br>
      <label><input type="radio" name="q3" value="B" /> B) A fixed value stored in memory</label><br>
      <label><input type="radio" name="q3" value="C" /> C) A container for modules</label><br>
      <label><input type="radio" name="q3" value="D" /> D) A loop that runs indefinitely</label>
    </form>
    <p class="feedback" id="feedback-q3"></p>
  </div>

  <div class="question" id="q4">
    <h3>4. When you assign a function to a variable without using parentheses, what are you storing in that variable?</h3>
    <form>
      <label><input type="radio" name="q4" value="A" /> A) The function object itself</label><br>
      <label><input type="radio" name="q4" value="B" /> B) The result of calling the function</label><br>
      <label><input type="radio" name="q4" value="C" /> C) An empty string</label><br>
      <label><input type="radio" name="q4" value="D" /> D) A syntax error</label>
    </form>
    <p class="feedback" id="feedback-q4"></p>
  </div>

  <div class="question" id="q5">
    <h3>5. In a function definition, what do we call the placeholders listed inside the parentheses?</h3>
    <form>
      <label><input type="radio" name="q5" value="A" /> A) Parameters</label><br>
      <label><input type="radio" name="q5" value="B" /> B) Arguments</label><br>
      <label><input type="radio" name="q5" value="C" /> C) Returns</label><br>
      <label><input type="radio" name="q5" value="D" /> D) Decorators</label>
    </form>
    <p class="feedback" id="feedback-q5"></p>
  </div>

  <div class="question" id="q6">
    <h3>6. When you provide actual values to a function call, what are those values called?</h3>
    <form>
      <label><input type="radio" name="q6" value="A" /> A) Arguments</label><br>
      <label><input type="radio" name="q6" value="B" /> B) Parameters</label><br>
      <label><input type="radio" name="q6" value="C" /> C) Variables</label><br>
      <label><input type="radio" name="q6" value="D" /> D) Objects</label>
    </form>
    <p class="feedback" id="feedback-q6"></p>
  </div>

  <div class="question" id="q7">
    <h3>7. What does a return statement do inside a function?</h3>
    <form>
      <label><input type="radio" name="q7" value="A" /> A) Sends a value back to the caller</label><br>
      <label><input type="radio" name="q7" value="B" /> B) Stops the entire program</label><br>
      <label><input type="radio" name="q7" value="C" /> C) Prints text to the console</label><br>
      <label><input type="radio" name="q7" value="D" /> D) Defines a new variable</label>
    </form>
    <p class="feedback" id="feedback-q7"></p>
  </div>

  <div class="question" id="q8">
    <h3>8. Which concept involves passing a function object to another function as an input?</h3>
    <form>
      <label><input type="radio" name="q8" value="A" /> A) Higher-order programming</label><br>
      <label><input type="radio" name="q8" value="B" /> B) Polymorphism</label><br>
      <label><input type="radio" name="q8" value="C" /> C) Recursion</label><br>
      <label><input type="radio" name="q8" value="D" /> D) Multithreading</label>
    </form>
    <p class="feedback" id="feedback-q8"></p>
  </div>

  <div class="question" id="q9">
    <h3>9. What term describes a function defined inside another function?</h3>
    <form>
      <label><input type="radio" name="q9" value="A" /> A) Nested function</label><br>
      <label><input type="radio" name="q9" value="B" /> B) Generator function</label><br>
      <label><input type="radio" name="q9" value="C" /> C) Static method</label><br>
      <label><input type="radio" name="q9" value="D" /> D) Abstract method</label>
    </form>
    <p class="feedback" id="feedback-q9"></p>
  </div>

  <div class="question" id="q10">
    <h3>10. What is a wrapper function?</h3>
    <form>
      <label><input type="radio" name="q10" value="A" /> A) A function that executes additional code before and/or after calling another function</label><br>
      <label><input type="radio" name="q10" value="B" /> B) A function without any parameters</label><br>
      <label><input type="radio" name="q10" value="C" /> C) A function that automatically handles exceptions</label><br>
      <label><input type="radio" name="q10" value="D" /> D) A function that only accepts integers</label>
    </form>
    <p class="feedback" id="feedback-q10"></p>
  </div>

  <div class="question" id="q11">
    <h3>11. If you attempt to call an inner function before it is defined, which error are you most likely to encounter?</h3>
    <form>
      <label><input type="radio" name="q11" value="A" /> A) NameError</label><br>
      <label><input type="radio" name="q11" value="B" /> B) TypeError</label><br>
      <label><input type="radio" name="q11" value="C" /> C) ValueError</label><br>
      <label><input type="radio" name="q11" value="D" /> D) ZeroDivisionError</label>
    </form>
    <p class="feedback" id="feedback-q11"></p>
  </div>

  <div class="question" id="q12">
    <h3>12. What happens when you print a function object without parentheses?</h3>
    <form>
      <label><input type="radio" name="q12" value="A" /> A) You see its representation (e.g., `<function ...>`)</label><br>
      <label><input type="radio" name="q12" value="B" /> B) The function executes and prints its output</label><br>
      <label><input type="radio" name="q12" value="C" /> C) Python concatenates it as a string</label><br>
      <label><input type="radio" name="q12" value="D" /> D) It raises a SyntaxError</label>
    </form>
    <p class="feedback" id="feedback-q12"></p>
  </div>

  <div class="question" id="q13">
    <h3>13. Which built-in function confirms that everything in Python is an object by revealing its type?</h3>
    <form>
      <label><input type="radio" name="q13" value="A" /> A) type()</label><br>
      <label><input type="radio" name="q13" value="B" /> B) instanceof()</label><br>
      <label><input type="radio" name="q13" value="C" /> C) kind()</label><br>
      <label><input type="radio" name="q13" value="D" /> D) objectof()</label>
    </form>
    <p class="feedback" id="feedback-q13"></p>
  </div>

  <div class="question" id="q14">
    <h3>14. Which example illustrates passing a function object as a parameter?</h3>
    <form>
      <label><input type="radio" name="q14" value="A" /> A) def apply(func, x): return func(x)</label><br>
      <label><input type="radio" name="q14" value="B" /> B) def apply(x, y): return x + y</label><br>
      <label><input type="radio" name="q14" value="C" /> C) def apply(): print("Hello")</label><br>
      <label><input type="radio" name="q14" value="D" /> D) def apply(func()): return x</label>
    </form>
    <p class="feedback" id="feedback-q14"></p>
  </div>

  <div class="question" id="q15">
    <h3>15. What does a decorator syntactically do in Python?</h3>
    <form>
      <label><input type="radio" name="q15" value="A" /> A) Wrap another function to modify its behavior</label><br>
      <label><input type="radio" name="q15" value="B" /> B) Change the class of an object at runtime</label><br>
      <label><input type="radio" name="q15" value="C" /> C) Inline C code into Python</label><br>
      <label><input type="radio" name="q15" value="D" /> D) Automatically document functions</label>
    </form>
    <p class="feedback" id="feedback-q15"></p>
  </div>

  <button onclick="checkAnswers()">Submit Answers</button>

  <script>
    const answers = {
      q1: "A",
      q2: "A",
      q3: "A",
      q4: "A",
      q5: "A",
      q6: "A",
      q7: "A",
      q8: "A",
      q9: "A",
      q10: "A",
      q11: "A",
      q12: "A",
      q13: "A",
      q14: "A",
      q15: "A"
    };

    function checkAnswers() {
      for (const [questionId, correct] of Object.entries(answers)) {
        const radios = document.querySelectorAll(`input[name="${questionId}"]`);
        let selected = null;
        radios.forEach(r => {
          if (r.checked) selected = r.value;
        });

        const feedback = document.getElementById(`feedback-${questionId}`);
        if (selected === correct) {
          feedback.textContent = "✅ Correct!";
          feedback.style.color = "green";
        } else {
          feedback.textContent = `❌ Incorrect. Correct answer: ${correct}`;
          feedback.style.color = "red";
        }
      }
    }
  </script>

  <!-- Shared Header and Footer JavaScript -->
  <script src="../../shared/header-footer.js"></script>
</body>
</html>