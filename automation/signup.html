<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Sign Up - Automation Practice</title>
  <meta name="description" content="Sign up page for Automation Practice. Perfect for QA engineers to practice Selenium, Playwright, and Cypress automation." />
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #4361ee;
      --primary-hover: #3a56d4;
      --secondary-color: #f8f9fa;
      --text-color: #333;
      --border-color: #e0e0e0;
      --error-color: #e63946;
      --success-color: #2a9d8f;
    }
    
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: 'Poppins', sans-serif;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      color: var(--text-color);
    }

    .progress-container {
      width: 100%;
      max-width: 450px;
      margin-bottom: 20px;
      background-color: #fff;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    }

    .progress-steps {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }

    .step {
      text-align: center;
      flex: 1;
      position: relative;
    }

    .step-circle {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background-color: #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 5px;
      color: #666;
      font-weight: bold;
    }

    .step.active .step-circle {
      background-color: var(--primary-color);
      color: white;
    }

    .step.completed .step-circle {
      background-color: var(--success-color);
      color: white;
    }

    .step-label {
      font-size: 12px;
      color: #666;
    }

    .step.active .step-label {
      color: var(--primary-color);
      font-weight: 500;
    }

    .step.completed .step-label {
      color: var(--success-color);
    }

    .progress-bar {
      height: 10px;
      background-color: #e0e0e0;
      border-radius: 5px;
      margin-top: 10px;
      overflow: hidden;
    }

    .progress-bar-fill {
      height: 100%;
      background-color: var(--primary-color);
      width: 33%;
      transition: width 0.5s ease;
    }

    .progress-text {
      text-align: center;
      margin-top: 5px;
      font-size: 14px;
      color: var(--primary-color);
      font-weight: 500;
    }

    .container {
      background: #fff;
      padding: 40px;
      border-radius: 16px;
      box-shadow: 0 10px 25px rgba(0,0,0,0.1);
      max-width: 450px;
      width: 100%;
      position: relative;
      overflow: hidden;
    }
    
    .container::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 8px;
      background: linear-gradient(90deg, var(--primary-color), #4cc9f0);
    }

    .container h2 {
      text-align: center;
      margin-bottom: 30px;
      color: var(--primary-color);
      font-weight: 600;
      font-size: 28px;
    }

    .form-group {
      margin-bottom: 24px;
      position: relative;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      font-size: 15px;
      color: #555;
    }

    input[type="text"], input[type="password"], input[type="email"] {
      width: 100%;
      padding: 14px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      font-size: 15px;
      transition: all 0.3s ease;
      background-color: #f9f9f9;
    }

    input:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
      outline: none;
      background-color: #fff;
    }

    button {
      width: 100%;
      padding: 14px;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    button:hover {
      background-color: var(--primary-hover);
    }

    .toggle {
      text-align: center;
      margin-top: 20px;
      font-size: 14px;
    }

    .toggle a {
      color: var(--primary-color);
      text-decoration: none;
      cursor: pointer;
      font-weight: 500;
    }

    .toggle a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <!-- Progress bar container -->
  <div class="progress-container">
    <h3>Your Progress</h3>
    <div class="progress-bar">
      <div class="progress-bar-fill" id="progressBarFill" style="width: 0% !important;"></div>
    </div>
    <div class="progress-text">Step 1 of 3: Sign Up (0%)</div>
  </div>

  <div class="container">
    <h2>Create Account</h2>
    <form id="signupForm">
      <div class="form-group">
        <label for="username">Username</label>
        <input type="text" id="username" name="username" placeholder="Choose a username" required />
      </div>

      <div class="form-group">
        <label for="email">Email</label>
        <input type="email" id="email" name="email" placeholder="Enter your email" required />
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" name="password" placeholder="Create a password" required />
      </div>

      <div class="form-group">
        <label for="confirmPassword">Confirm Password</label>
        <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Confirm your password" required />
      </div>

      <button type="submit">Sign Up</button>

      <div class="toggle">
        Already have an account? <a href="login.html">Login</a>
      </div>
    </form>
  </div>

  <script>
    // Add this at the very top of your script
    document.addEventListener('DOMContentLoaded', function() {
      // Clear all localStorage to reset everything
      localStorage.clear();
      console.log('Cleared all localStorage');
      
      // Initialize empty registered users
      let registeredUsers = {};
      localStorage.setItem('registeredUsers', JSON.stringify(registeredUsers));
      
      // Set progress bar to 0%
      const progressBarFill = document.getElementById('progressBarFill');
      progressBarFill.style.width = '0%';
      document.querySelector('.progress-text').textContent = 'Step 1 of 3: Sign Up (0%)';
    });
    
    // Load registered users from localStorage
    let registeredUsers = JSON.parse(localStorage.getItem('registeredUsers')) || {};
    
    // Initialize progress bar - FIXED VERSION
    document.addEventListener('DOMContentLoaded', function() {
      // Set progress bar to 0% for signup page
      const progressBarFill = document.getElementById('progressBarFill');
      progressBarFill.style.width = '0%';
      document.querySelector('.progress-text').textContent = 'Step 1 of 3: Sign Up (0%)';
      
      // Clear any previous progress that might be causing issues
      localStorage.removeItem('currentProgress');
      
      // Only update if there's a current user with higher progress
      const currentUser = localStorage.getItem('currentUser');
      if (currentUser && registeredUsers[currentUser] && registeredUsers[currentUser].progress > 0) {
        const userProgress = registeredUsers[currentUser].progress;
        // Only update if they've actually completed later steps
        if (userProgress > 0) {
          progressBarFill.style.width = userProgress + '%';
          document.querySelector('.progress-text').textContent = 
            `Progress: ${userProgress}% complete`;
        }
      }
    });
    
    // Handle form submit
    document.getElementById('signupForm').addEventListener('submit', function(e) {
      e.preventDefault();

      const username = document.getElementById('username').value.trim();
      const email = document.getElementById('email').value.trim();
      const password = document.getElementById('password').value;
      const confirmPassword = document.getElementById('confirmPassword').value;

      // Basic validation
      if (!username || !email || !password || !confirmPassword) {
        alert('Please fill out all required fields.');
        return;
      }

      if (password !== confirmPassword) {
        alert('Passwords do not match.');
        return;
      }
      
      if (registeredUsers[username]) {
        alert('Username already exists. Please choose another one.');
        return;
      }
      
      // Update progress bar to 33% before redirecting
      const progressBarFill = document.getElementById('progressBarFill');
      progressBarFill.style.width = '33%';
      document.querySelector('.progress-text').textContent = 'Step 1 of 3: Sign Up (33%)';
      
      // Register new user with 33% progress (completed step 1 of 3)
      registeredUsers[username] = {
        password: password,
        email: email,
        progress: 33
      };
      
      localStorage.setItem('registeredUsers', JSON.stringify(registeredUsers));
      localStorage.setItem('currentUser', username);
      
      // Add a small delay to show the progress update before redirecting
      setTimeout(function() {
        alert('Signup successful! Proceeding to login...');
        window.location.href = 'login.html';
      }, 500);
    });
  </script>
</body>
</html>
