body {
    font-family: 'Poppins', sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
    color: #333;
    line-height: 1.6;
}

header {
    text-align: center;
    padding: 40px 20px;
    background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

#progress-bar {
    width: 80%;
    height: 20px;
    background-color: rgba(255, 255, 255, 0.3);
    margin: 20px auto 0;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

#progress {
    width: 0%;
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #8bc34a);
    border-radius: 10px;
    transition: width 0.5s ease-in-out;
}

/* New sticky progress container */
.sticky-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
    padding: 10px 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    display: none;
}

.sticky-progress #sticky-progress-bar {
    width: 80%;
    height: 15px;
    background-color: rgba(255, 255, 255, 0.3);
    margin: 0 auto;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.sticky-progress #sticky-progress {
    width: 0%;
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #8bc34a);
    border-radius: 10px;
    transition: width 0.5s ease-in-out;
}

nav {
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

nav ul {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    list-style-type: none;
    padding: 0;
    margin: 0;
}

nav li {
    margin: 0 15px;
}

nav a {
    text-decoration: none;
    color: #2541b2;
    font-weight: 600;
    padding: 8px 12px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

nav a:hover {
    background-color: #f0f4ff;
    color: #4a6bff;
}

main {
    max-width: 1200px;
    margin: 30px auto;
    padding: 0 20px;
}

section {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

section:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

h2 {
    color: #2541b2;
    border-bottom: 2px solid #f0f4ff;
    padding-bottom: 10px;
    margin-top: 0;
}

button {
    background: linear-gradient(135deg, #4a6bff 0%, #2541b2 100%);
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: 15px;
}

button:hover {
    background: linear-gradient(135deg, #5a77ff 0%, #3551c2 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

input, select {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    margin: 10px 0;
    width: 100%;
    max-width: 300px;
}

label {
    display: block;
    margin: 10px 0 5px;
    font-weight: 500;
}

#drag-container {
    width: 200px;
    height: 60px;
    background-color: #f0f4ff;
    margin: 15px 0;
    border-radius: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
}

#draggable {
    background: linear-gradient(135deg, #ff4a6b 0%, #b22541 100%);
    color: white;
    padding: 15px;
    text-align: center;
    cursor: grab;
    border-radius: 5px;
    width: 80%;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin: 0 auto;
}

#drop-container {
    width: 250px;
    height: 100px;
    background-color: #f0f4ff;
    margin: 15px 0;
    text-align: center;
    border: 2px dashed #4a6bff;
    border-radius: 5px;
    transition: all 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

#drop-container.empty {
    line-height: 100px;
}

#drop-container:not(.empty) {
    line-height: normal;
}

#drop-container:hover {
    background-color: #e6ecff;
}

footer {
    text-align: center;
    background: linear-gradient(135deg, #2541b2 0%, #1a2d80 100%);
    color: white;
    padding: 20px;
    margin-top: 50px;
}

/* Result displays */
[id$="-result"] {
    margin-top: 15px;
    padding: 10px;
    background-color: #f0f4ff;
    border-left: 4px solid #4a6bff;
    border-radius: 0 5px 5px 0;
    font-weight: 500;
    transition: all 0.3s ease;
}

/* Responsive design */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
        align-items: center;
    }
    
    nav li {
        margin: 5px 0;
    }
    
    section {
        padding: 20px;
    }
}
