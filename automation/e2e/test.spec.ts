import { test, expect } from '@playwright/test';

test('Mission 7 hidden persists via localStorage', async ({ page }) => {
  await page.goto('http://127.0.0.1:5500/automation/playwright-selenium-cypress-practice.html');

  const mission9 = page.locator('#persistent-hide-btn');
  const mission7 = page.getByRole('heading', { level: 2, name: /Mission 7/ });

  // Initial state checks
  await expect(mission7).toBeVisible();
  expect(await page.evaluate(() => localStorage.getItem('mission7PersistentHide')))
    .toBe('false');

  // Hide via Mission 9
  await mission9.click();

  // After-click checks
  await expect(mission7).toBeHidden();
  expect(await page.evaluate(() => localStorage.getItem('mission7PersistentHide')))
    .toBe('true');

  // Refresh & final checks
  await page.reload();
  await expect(mission7).toBeHidden();
  expect(await page.evaluate(() => localStorage.getItem('mission7PersistentHide')))
    .toBe('true');
});
