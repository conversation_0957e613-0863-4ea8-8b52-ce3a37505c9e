<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Learning Resources & Automation Practice - <PERSON><PERSON></title>
  <meta name="description" content="Access interactive programming quizzes and automation testing practice environments. Perfect for developers and QA engineers to enhance skills in Python, Java, web development, and test automation.">
  <meta name="keywords" content="programming quizzes, automation testing practice, QA engineering, Python quiz, Java quiz, web development, Selenium practice, Playwright testing, Cypress automation">
  <link rel="stylesheet" href="styles.css">
  <!-- Add Font Awesome for social media icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- Google AdSense Code -->
  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8655026160093979" crossorigin="anonymous"></script>
  
  <style>
    /* Additional styles for quiz resources page */
    .resources-container {
      max-width: 1200px;
      margin: 10px auto;
      padding: 15px;
    }
    
    h2.section-title {
      text-align: center;
      margin-bottom: 10px;
      color: #ffffff;
    }
    
    .resource-intro {
      text-align: center;
      margin-bottom: 10px;
    }

    .resource-intro p {
      max-width: 800px;
      margin: 0 auto;
      color: #ffffff;
      line-height: 1.4;
    }

    .ad-container {
      width: 100%;
      margin: 10px 0;
      text-align: center;
      clear: both;
      min-height: 90px;
      background-color: #f5f5f5;
      border-radius: 5px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .quiz-section {
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      padding: 15px;
      margin-top: 10px;
      margin-bottom: 15px;
    }
    
    .quiz-nav {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 0;
    }
    
    .quiz-category {
      position: relative;
    }
    
    /* Updated button styles with different colors for each category */
    .category-button.python {
      background-color: #3776AB; /* Python blue */
      box-shadow: 0 4px 8px rgba(55, 118, 171, 0.3);
    }

    .category-button.java {
      background-color: #E76F00; /* Java orange */
      box-shadow: 0 4px 8px rgba(231, 111, 0, 0.3);
    }

    .category-button.web {
      background-color: #2965f1; /* CSS blue */
      box-shadow: 0 4px 8px rgba(41, 101, 241, 0.3);
    }

    .category-button.ged {
      background-color: #7248b6; /* Purple */
      box-shadow: 0 4px 8px rgba(114, 72, 182, 0.3);
    }

    .category-button.automation {
      background-color: #ff5722; /* Orange color for automation */
      box-shadow: 0 4px 8px rgba(255, 87, 34, 0.3);
    }

    .category-button.version-control {
      background-color: #f39c12; /* Git orange/yellow */
      box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
    }

    .category-button.ai {
      background-color: #9b59b6; /* Purple for AI */
      box-shadow: 0 4px 8px rgba(155, 89, 182, 0.3);
    }

    .category-button.arduino {
      background-color: #00979D; /* Teal/cyan for Arduino */
      box-shadow: 0 4px 8px rgba(0, 151, 157, 0.3);
    }

    /* Modern sleek button style updates */
    .category-button {
      background-color: #0077b5;
      color: white;
      border: none;
      padding: 12px 20px;
      font-size: 15px;
      border-radius: 8px; /* Slightly more rounded */
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 500;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      letter-spacing: 0.5px;
    }

    .category-button:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    }
    
    .quiz-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      z-index: 10;
      background-color: white;
      min-width: 250px;
      border-radius: 5px;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
      padding: 10px 0;
      display: none;
    }
    
    .quiz-category:hover .quiz-dropdown {
      display: block;
    }
    
    .quiz-dropdown h3 {
      color: #333;
      margin: 0;
      padding: 10px 15px;
      border-bottom: 1px solid #eee;
      font-size: 16px;
    }
    
    .quiz-dropdown ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    
    .quiz-dropdown li {
      padding: 8px 15px;
      transition: background-color 0.2s;
      border-bottom: 1px solid #f5f5f5;
    }
    
    .quiz-dropdown li:last-child {
      border-bottom: none;
    }
    
    .quiz-dropdown li:hover {
      background-color: #f5f7fa;
    }
    
    .quiz-dropdown a {
      color: #333;
      text-decoration: none;
      display: block;
      font-size: 14px;
    }
    
    .quiz-dropdown a:hover {
      color: #0077b5;
    }
    
    .content-area {
      margin-top: 15px;
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      padding: 15px;
    }
    
    .content-area h2 {
      margin-top: 0;
      margin-bottom: 10px;
    }
    
    .content-area p {
      color: #555;
      margin-bottom: 20px;
    }
    
    /* New feature grid */
    .features-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
      margin-top: 25px;
      text-align: left;
    }
    
    .feature {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 15px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s;
    }
    
    .feature:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .feature i {
      color: #0077b5;
      font-size: 20px;
    }
    
    .feature p {
      margin: 0;
      font-size: 14px;
    }
    
    .ad-container {
      width: 100%;
      margin: 20px 0;
      text-align: center;
      clear: both;
      min-height: 90px;
      background-color: #f5f5f5;
      border-radius: 5px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .resource-intro {
      text-align: center;
      margin-bottom: 25px;
    }
    
    .resource-intro p {
      max-width: 800px;
      margin: 0 auto;
      color: #ffffff;
      line-height: 1.6;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
      .features-grid {
        grid-template-columns: 1fr;
      }
      
      .quiz-nav {
        flex-direction: column;
        align-items: center;
      }
      
      .quiz-category {
        width: 100%;
        max-width: 300px;
      }
      
      .category-button {
        width: 100%;
      }
      
      .quiz-dropdown {
        width: 100%;
      }
    }

    /* Header styling to match index.html */
    header {
      background-color: #ffffff;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 15px 0;
      position: sticky;
      top: 0;
      z-index: 1000;
    }

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      flex-wrap: wrap;
    }

    .logo-container {
      margin-right: 20px;
    }

    .home-button {
      display: flex;
      align-items: center;
      background-color: #0077b5;
      color: white;
      padding: 8px 15px;
      border-radius: 5px;
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .home-button:hover {
      background-color: #005d8f;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .home-button i {
      margin-right: 5px;
      font-size: 16px;
    }

    .headline {
      text-align: center;
      flex-grow: 1;
    }

    .headline h1 {
      margin: 0;
      font-size: 28px;
      color: #333;
    }

    .headline p {
      margin: 5px 0 0;
      color: #555;
      font-size: 14px;
    }

    nav {
      margin-left: auto;
    }

    nav ul {
      display: flex;
      list-style: none;
      margin: 0;
      padding: 0;
      gap: 15px;
    }

    nav a {
      color: #333;
      text-decoration: none;
      font-weight: 500;
      padding: 5px 0;
      position: relative;
      transition: color 0.3s;
    }

    nav a:hover, nav a.active {
      color: #0077b5;
    }

    nav a.active:after, nav a:hover:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: #0077b5;
    }

    .social-icons {
      display: flex;
      gap: 15px;
      margin-left: 20px;
    }

    .social-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: white;
      color: #333;
      transition: transform 0.3s, box-shadow 0.3s;
      border: 1px solid #eaeaea;
    }

    .social-icon:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .social-icon.linkedin {
      color: #0077b5;
    }

    .social-icon.youtube {
      color: #ff0000;
    }

    .social-icon.facebook {
      color: #1877f2;
    }

    .social-icon.linkedin:hover {
      background-color: #0077b5;
      color: white;
    }

    .social-icon.youtube:hover {
      background-color: #ff0000;
      color: white;
    }

    .social-icon.facebook:hover {
      background-color: #1877f2;
      color: white;
    }

    /* Responsive adjustments for header */
    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        text-align: center;
      }
      
      .logo-container {
        margin: 0 0 15px 0;
      }
      
      nav {
        margin: 15px 0;
      }
      
      nav ul {
        flex-wrap: wrap;
        justify-content: center;
      }
      
      .social-icons {
        margin: 15px 0 0 0;
      }
    }

    /* Enhanced styling for GED quiz dropdown */
    .quiz-dropdown h4 {
      margin: 15px 15px 8px;
      color: #7248b6; /* Matches the GED button color */
      font-size: 15px;
      border-bottom: 1px solid #e0d5f0;
      padding-bottom: 5px;
      font-weight: 600;
    }

    .quiz-dropdown ul {
      margin-bottom: 10px;
    }

    .quiz-dropdown ul li a {
      padding: 6px 15px;
      display: block;
      transition: background-color 0.2s;
    }

    .quiz-dropdown ul li a:hover {
      background-color: #f5f0ff;
    }

    /* Make the dropdown wider to accommodate longer category names */
    .quiz-category .quiz-dropdown {
      min-width: 280px;
    }
  </style>
</head>
<body>
  <header>
    <div class="header-content">
      <div class="logo-container">
        <a href="index.html" class="home-button" aria-label="Home">
          <i class="fas fa-home"></i> Home
        </a>
      </div>
      <div class="headline">
        <h1>Faruk Hasan</h1>
        <p><b>Software QA Engineer | Automation & AI-Driven Testing Specialist</b></p>
      </div>
      <nav>
        <ul>
          <li><a href="index.html#about">About Me</a></li>
          <li><a href="index.html#career">Career</a></li>
          <li><a href="index.html#courses">Courses</a></li>
          <li><a href="index.html#projects">Projects</a></li>
          <li><a href="resources.html" class="active">Resources</a></li>
          <li><a href="index.html#investment">Investment</a></li>
          <li><a href="index.html#contact">Contact</a></li>
        </ul>
      </nav>
      <div class="social-icons">
        <a href="https://www.linkedin.com/in/md-faruk-hasan/" class="social-icon linkedin" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
        <a href="https://www.youtube.com/@kidz_code" class="social-icon youtube" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
        <a href="https://www.facebook.com/HasanMd2020/" class="social-icon facebook" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
      </div>
    </div>
  </header>

  <div class="resources-container">
    <h2 class="section-title">Learning Resources</h2>
    
    <div class="resource-intro">
      <p>Welcome to my comprehensive learning resources section. Here you'll find interactive quizzes to test your knowledge on various programming languages, web development topics, and a dedicated automation practice environment for QA engineers. Enhance your skills in Python, Java, HTML/CSS/JavaScript, or practice automation testing with Selenium, Playwright, or Cypress. Choose a category below to start learning and testing your knowledge.</p>
    </div>
    
    <!-- Quiz navigation moved above the ad -->
    <div class="quiz-section">
      <div class="quiz-nav">
        <div class="quiz-category">
          <button class="category-button python">Python Quizzes</button>
          <div class="quiz-dropdown">
            <h3>Python Quizzes</h3>
            <ul>
              <li><a href="quizzes/python-basics.html">Python Basics Quiz</a></li>
              <li><a href="quizzes/python-functions.html">Python Functions & Methods</a></li>
              <li><a href="quizzes/python-oop.html">Object-Oriented Python</a></li>
              <li><a href="quizzes/python-data-structures.html">Python Data Structures</a></li>
              <li><a href="quizzes/python-file-handling.html">File Handling in Python</a></li>
              <li><a href="quizzes/python-modules.html">Python Modules & Packages</a></li>
              <li><a href="quizzes/python_quiz_advance_1.html">Advanced Python Quiz 1</a></li>
              <li><a href="quizzes/PYTHON/python_playwright_quiz_1.html">Playwright Python Quiz</a></li>
              <li><a href="quizzes/PYTHON/python_decorator_quiz.html">Python Decorators Quiz</a></li>
              <li><a href="quizzes/PYTHON/data_scraping_quiz.html">Data Scraping Quiz</a></li>
              <li><a href="quizzes/PYTHON/python_dictionary_quiz.html">Python Dictionary Quiz</a></li>
              <li><a href="quizzes/PYTHON/python_oop_quiz_2.html">Python OOP Quiz 2</a></li>
              <li><a href="quizzes/PYTHON/chat_bot_rule_based_quiz.html">Python Chatbot Quiz</a></li>
            </ul>
          </div>
        </div>

        <div class="quiz-category">
          <button class="category-button ai">AI Quizzes</button>
          <div class="quiz-dropdown">
            <h3>AI & Data Science Quizzes</h3>
            <ul>
              <li><a href="quizzes/AI/ai_quiz_1_web_scraping.html">AI Quiz 1: Web Scraping</a></li>
              <li><a href="quizzes/AI/ai_quiz_2_numpy_pandas.html">AI Quiz 2: NumPy & Pandas</a></li>
              <li><a href="quizzes/AI/ai_quiz_3_bag_of_words.html">AI Quiz 3: Bag of Words</a></li>
              <li><a href="quizzes/AI/ai_quiz_0_fundamentals.html">Data & AI Fundamentals</a></li>
            </ul>
          </div>
        </div>

        <div class="quiz-category">
          <button class="category-button arduino">Arduino & Electronics Quizzes</button>
          <div class="quiz-dropdown">
            <h3>Arduino & Basic Electronics Quizzes</h3>
            <ul>
              <li><a href="quizzes/Arduino/arduino_board_basic_quiz.html">Electronics Basics Quiz</a></li>
              <li><a href="quizzes/Arduino/arduino_board_basic_electronics_quiz.html">Arduino Basics Mastery Quiz</a></li>
            </ul>
          </div>
        </div>

        <div class="quiz-category">
          <button class="category-button java">Java Quizzes</button>
          <div class="quiz-dropdown">
            <h3>Java Quizzes</h3>
            <ul>
              <li><a href="quizzes/java-basics.html">Java Basics Quiz</a></li>
              <li><a href="quizzes/java-oop.html">Java OOP Concepts</a></li>
              <li><a href="quizzes/java-collections.html">Java Collections Framework</a></li>
              <li><a href="quizzes/java-inheritance.html">Inheritance & Polymorphism</a></li>
              <li><a href="quizzes/java-exceptions.html">Exception Handling in Java</a></li>
              <li><a href="quizzes/java-io.html">Java I/O Operations</a></li>
              <li><a href="quizzes/java-threads.html">Java Multithreading</a></li>
            </ul>
          </div>
        </div>

        <div class="quiz-category">
          <button class="category-button version-control">Version Control Quizzes</button>
          <div class="quiz-dropdown">
            <h3>Version Control Quizzes</h3>
            <ul>
              <li><a href="quizzes/GIT/git_quiz_basics.html">Git & GitHub Basics Quiz</a></li>
            </ul>
          </div>
        </div>

        <div class="quiz-category">
          <button class="category-button web">HTML, CSS, JavaScript Quizzes</button>
          <div class="quiz-dropdown">
            <ul>
              <li><a href="#" class="quiz-link" data-quiz="Quiz-1 HTML" data-url="quizzes/HTML-CSS-JS/wd_quiz1.html">Quiz-1 HTML</a></li>
              <li><a href="#" class="quiz-link" data-quiz="HTML Fundamentals" data-url="quizzes/HTML-CSS-JS/wd_project_4_quiz1.html">Project-4 Quiz:1</a></li>
              <li><a href="#" class="quiz-link" data-quiz="Project-4" data-url="quizzes/HTML-CSS-JS/wd_project_4_quiz2.html">Project-4 Quiz:2</a></li>
              <li><a href="quizzes/HTML-CSS-JS/project5_quiz2.html">Project-5 Quiz:2</a></li>
              <li><a href="quizzes/css-basics.html">CSS Styling Basics</a></li>
              <li><a href="quizzes/css-layout.html">CSS Layout & Flexbox</a></li>
              <li><a href="quizzes/js-basics.html">JavaScript Fundamentals</a></li>
              <li><a href="quizzes/js-dom.html">DOM Manipulation</a></li>
              <li><a href="quizzes/js-events.html">JavaScript Events</a></li>
              <li><a href="quizzes/js-forms.html">Form Validation</a></li>
              <li><a href="quizzes/web-concepts.html">Web Development Concepts</a></li>
            </ul>
          </div>
        </div>
        
        <!-- GED Math Quizzes - Direct link instead of dropdown -->
        <div class="quiz-category">
          <a href="quizzes/ged-math-quizzes.html" class="category-button ged" style="display: inline-block; text-decoration: none;">GED Math Quizzes</a>
        </div>
        
        <!-- New Automation Practice Button -->
        <div class="quiz-category">
          <a href="automation/signup.html" class="category-button automation" style="display: inline-block; text-decoration: none;">Automation Practice</a>
        </div>
      </div>
      
      <!-- Content area with improved styling -->
      <div class="content-area">
        <h2>Interactive Quiz Section</h2>
        <p>Select any quiz category above to begin testing your knowledge!</p>
        <div class="features-grid">
          <div class="feature">
            <i class="fas fa-check-circle"></i>
            <p>Immediate feedback on your answers</p>
          </div>
          <div class="feature">
            <i class="fas fa-lightbulb"></i>
            <p>Detailed explanations for each question</p>
          </div>
          <div class="feature">
            <i class="fas fa-chart-line"></i>
            <p>Track your progress over time</p>
          </div>
          <div class="feature">
            <i class="fas fa-medal"></i>
            <p>Earn badges for completing quizzes</p>
          </div>
        </div>
      </div>
      
      <!-- Middle AdSense -->
      <div class="ad-container">
        <!-- Google AdSense -->
        <ins class="adsbygoogle"
             style="display:block"
             data-ad-client="ca-pub-8655026160093979"
             data-ad-slot="YOUR_AD_SLOT_2"
             data-ad-format="auto"
             data-full-width-responsive="true"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
      </div>
    </div>
    
    <!-- Bottom AdSense -->
    <div class="ad-container">
      <!-- Google AdSense -->
      <ins class="adsbygoogle"
           style="display:block"
           data-ad-client="ca-pub-8655026160093979"
           data-ad-slot="YOUR_AD_SLOT_3"
           data-ad-format="auto"
           data-full-width-responsive="true"></ins>
      <script>
           (adsbygoogle = window.adsbygoogle || []).push({});
      </script>
    </div>
  </div>

  <script>
    // Updated script to handle quiz selection and navigation
    document.querySelectorAll('.quiz-dropdown a').forEach(link => {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        const quizName = this.textContent;
        const contentArea = document.querySelector('.content-area');
        
        // Get the custom data attribute for the quiz URL
        const quizUrl = this.dataset.url || '';
        
        contentArea.innerHTML = `
          <h2>${quizName}</h2>
          <p>You've selected the ${quizName} quiz. This quiz will test your knowledge of key concepts in this area.</p>
          <button id="startQuizBtn" style="padding: 10px 20px; background-color: #0077b5; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 20px;">Start Quiz</button>
        `;
        
        // Add click event to the start quiz button
        const startQuizBtn = document.getElementById('startQuizBtn');
        startQuizBtn.addEventListener('click', function() {
          // If it's the HTML Fundamentals quiz or has a custom URL, navigate to that page
          if (quizName === 'HTML Fundamentals' || quizUrl) {
            window.location.href = quizUrl || 'wd_project_4_quiz1.html';
          } else {
            // For other quizzes, navigate to their respective pages
            // This keeps the original behavior for non-HTML Fundamentals quizzes
            const quizPageUrl = link.getAttribute('href');
            if (quizPageUrl && quizPageUrl !== '#') {
              window.location.href = quizPageUrl;
            }
          }
        });
      });
    });
  </script>

  <!-- Footer -->
  <footer>
    <div class="footer-content">
      <div class="footer-info">
        <div class="footer-logo">Faruk Hasan</div>
        <p>QA Engineer | Automation & AI-Driven Testing Specialist</p>
        <p>Empowering developers and testers with knowledge and tools for success.</p>
      </div>

      <div class="footer-social">
        <h4>Connect With Me</h4>
        <div class="social-icons-footer">
          <a href="https://www.linkedin.com/in/md-faruk-hasan/" class="social-icon linkedin" aria-label="LinkedIn" target="_blank"><i class="fab fa-linkedin"></i></a>
          <a href="https://www.youtube.com/@kidz_code" class="social-icon youtube" aria-label="YouTube" target="_blank"><i class="fab fa-youtube"></i></a>
          <a href="https://www.facebook.com/HasanMd2020/" class="social-icon facebook" aria-label="Facebook" target="_blank"><i class="fab fa-facebook-f"></i></a>
          <a href="https://github.com/faruklmu17" class="social-icon github" aria-label="GitHub" target="_blank"><i class="fab fa-github"></i></a>
        </div>
      </div>

      <div class="footer-share">
        <h4>Share This Page</h4>
        <div class="share-buttons">
          <a href="javascript:void(0)" onclick="shareOnFacebook()" class="share-btn facebook" aria-label="Share on Facebook"><i class="fab fa-facebook-f"></i></a>
          <a href="javascript:void(0)" onclick="shareOnTwitter()" class="share-btn twitter" aria-label="Share on Twitter"><i class="fab fa-twitter"></i></a>
          <a href="javascript:void(0)" onclick="shareOnLinkedIn()" class="share-btn linkedin" aria-label="Share on LinkedIn"><i class="fab fa-linkedin-in"></i></a>
          <a href="javascript:void(0)" onclick="shareByEmail()" class="share-btn email" aria-label="Share by Email"><i class="fas fa-envelope"></i></a>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <p>&copy; 2025 Faruk Hasan. All rights reserved. |
        <a href="index.html">Home</a> |
        <a href="resources.html">Resources</a>
      </p>
    </div>
  </footer>

  <!-- Social sharing functions -->
  <script>
    function shareOnFacebook() {
      const url = encodeURIComponent(window.location.href);
      const title = encodeURIComponent(document.title);
      window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank', 'width=600,height=400');
    }

    function shareOnTwitter() {
      const url = encodeURIComponent(window.location.href);
      const title = encodeURIComponent(document.title);
      window.open(`https://twitter.com/intent/tweet?url=${url}&text=${title}`, '_blank', 'width=600,height=400');
    }

    function shareOnLinkedIn() {
      const url = encodeURIComponent(window.location.href);
      window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank', 'width=600,height=400');
    }

    function shareByEmail() {
      const url = window.location.href;
      const title = document.title;
      const body = `Check out this page: ${title}\n${url}`;
      window.location.href = `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(body)}`;
    }
  </script>

  <!-- Cosmic Galaxy Background Art -->
  <script src="ai_resources/art.js"></script>
</body>
</html>
