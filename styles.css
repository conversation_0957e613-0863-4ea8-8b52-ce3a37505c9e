:root {
  --primary-color: #3a6ea5;
  --secondary-color: #2c3e50;
  --background-color: #f4f7f6;
  --text-color: #333;
  --accent-color: #2980b9;
  --light-background: #ffffff;
  --soft-shadow: 0 10px 25px rgba(0,0,0,0.05);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-color);
}

.container {
  width: 90%;
  max-width: 1100px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* Enhanced Header Styling with larger round profile image */
header {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 0.5rem 0;  /* Small padding to give some space */
  transition: all 0.3s ease;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding-right: 2rem;
}

.profile-image {
  width: 90px;  /* Large but not too large */
  height: 90px;  /* Keep it square before border-radius */
  border-radius: 50%;  /* Make it round */
  overflow: hidden;
  border: 3px solid var(--primary-color);  /* Add border all around */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  margin: 0.5rem;
}

.profile-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.profile-image:hover img {
  transform: scale(1.05);
}

.headline {
  text-align: left;
  padding: 0.5rem 0;
}

.headline h1 {
  font-family: 'Dancing Script', cursive;
  font-size: 3.2rem;
  color: #0077b5;
  margin-bottom: 0.5rem;
}

.nav-social-container {
  display: flex;
  align-items: center;
  padding: 0.8rem 2rem;
}

/* Medium screen adjustments to prevent navigation wrapping */
@media (max-width: 1200px) and (min-width: 901px) {
  header nav ul {
    gap: 0.8rem;
  }

  header nav ul li a {
    padding: 0.5rem 0.5rem;
    font-size: 0.85rem;
  }
}

/* Responsive adjustments */
@media (max-width: 900px) {
  .header-content {
    flex-direction: column;
    padding: 0.5rem;
  }
  
  .header-left {
    width: 100%;
    margin-bottom: 1rem;
    padding-right: 0;
    justify-content: center;
  }
  
  .profile-image {
    width: 100px;
    height: 100px;
    margin: 0.5rem 1rem 0.5rem 0;
  }
  
  .headline {
    text-align: left;
  }
  
  .headline h1 {
    font-size: 2.8rem;
  }
  
  .nav-social-container {
    width: 100%;
    justify-content: center;
    padding: 0.8rem;
  }
}

@media (max-width: 600px) {
  .header-left {
    flex-direction: column;
    align-items: center;
  }
  
  .profile-image {
    width: 120px;
    height: 120px;
    margin: 0.5rem auto;
  }
  
  .headline {
    text-align: center;
    padding: 0.5rem;
  }
}

.headline p {
  color: var(--secondary-color);
  font-size: 0.9rem;
  font-weight: 400;
}

.headline .highlight {
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 600;
}

.nav-social-container {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

header nav {
  display: flex;
  justify-content: flex-end;
}

header nav ul {
  display: flex;
  list-style: none;
  gap: 1rem;
  flex-wrap: nowrap;
}

header nav ul li a {
  text-decoration: none;
  color: var(--secondary-color);
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 0.5rem 0.6rem;
  border-radius: 6px;
  font-size: 0.9rem;
  position: relative;
  white-space: nowrap;
}

header nav ul li a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  transition: width 0.3s ease, left 0.3s ease;
}

header nav ul li a:hover {
  color: var(--primary-color);
}

header nav ul li a:hover::after {
  width: 80%;
  left: 10%;
}

.social-icons {
  display: flex;
  gap: 1rem;
  margin-left: 1rem;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42px;  /* Increased from 36px */
  height: 42px; /* Increased from 36px */
  border-radius: 50%;
  transition: all 0.3s ease;
  font-size: 1.2rem; /* Increased from 1rem */
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
}

/* Brand-specific styling */
.social-icon.linkedin {
  color: #0077B5;
  border-color: rgba(0, 119, 181, 0.3);
}

.social-icon.youtube {
  color: #FF0000;
  border-color: rgba(255, 0, 0, 0.3);
}

.social-icon.facebook {
  color: #1877F2;
  border-color: rgba(24, 119, 242, 0.3);
}

/* Hover effects */
.social-icon:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.social-icon.linkedin:hover {
  background-color: #0077B5;
  color: white;
  border-color: #0077B5;
}

.social-icon.youtube:hover {
  background-color: #FF0000;
  color: white;
  border-color: #FF0000;
}

.social-icon.facebook:hover {
  background-color: #1877F2;
  color: white;
  border-color: #1877F2;
}

/* Responsive adjustments */
@media (max-width: 900px) {
  .social-icons {
    margin-top: 0.5rem;
    margin-left: 0;
  }
  
  .social-icon {
    width: 40px;
    height: 40px;
  }
}

/* Responsive design */
@media (max-width: 900px) {
  header {
    padding: 0.8rem 1rem;
  }
  
  .header-content {
    flex-direction: column;
    padding: 0.8rem 0;
  }
  
  .headline {
    margin-bottom: 1rem;
    text-align: center;
  }
  
  .nav-social-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  header nav ul {
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.8rem;
  }
  
  .social-icons {
    margin-top: 0.5rem;
  }
}

section {
  background-color: var(--light-background);
  border-radius: 12px;
  box-shadow: var(--soft-shadow);
  margin-bottom: 2rem;
  padding: 3rem;
  transition: transform 0.2s ease;
}

section:hover {
  transform: translateY(-3px);
}
/* 
#about {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  padding: 2rem;
  position: relative;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.about-image {
  flex-shrink: 0;
}

.about-image img {
  width: 160px;
  height: 160px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.about-content {
  flex: 1;
}

.about-content h1 {
  font-size: 1.8rem;
  margin-bottom: 0.8rem;
  font-weight: 700;
  position: relative;
}

.about-content h1::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: white;
}

.about-content p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
} */

/* Two-column About Section to match Courses and YouTube sections */
#about {
  width: 100%;
  padding: 3rem 0;
  box-sizing: border-box;
  background-color: #fcfcfd;
}

.about-container.two-column {
  width: 90%;
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

/* Style each column */
.about-bio, .career-highlights {
  background-color: white;
  border-radius: 10px;
  padding: 1.75rem;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  height: 100%;
}

.about-bio:hover, .career-highlights:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.08);
}

#about .section-title {
  text-align: center;
  margin-bottom: 1.5rem;
  color: var(--secondary-color);
  position: relative;
  font-weight: 600;
}

#about .section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

.about-content {
  display: flex;
  flex-direction: column;
  margin-top: 1rem;
}

.about-text {
  width: 100%;
}

.about-text p {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.about-intro {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1.2rem;
  font-weight: 500;
  color: var(--secondary-color);
}

.name-highlight {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  color: var(--primary-color);
  position: relative;
  display: inline-block;
}

/* Career Highlights styling */
.career-card.compact {
  background-color: #f9f9fc;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.career-card.compact:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.career-card.compact .career-header {
  margin-bottom: 0.8rem;
}

.career-card.compact h3 {
  font-size: 1.1rem;
  margin-bottom: 0.3rem;
  color: var(--secondary-color);
}

.career-card.compact .company-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: #666;
}

.career-card.compact .achievements {
  list-style: none;
  padding: 0;
  margin: 0;
}

.career-card.compact .achievements li {
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
  color: var(--secondary-color);
  position: relative;
  padding-left: 1rem;
  line-height: 1.4;
}

.career-card.compact .achievements li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: var(--primary-color);
}

.view-more-link {
  display: inline-block;
  margin-top: 1rem;
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.view-more-link i {
  margin-left: 0.3rem;
  transition: transform 0.3s ease;
}

.view-more-link:hover {
  color: var(--accent-color);
}

.view-more-link:hover i {
  transform: translateX(3px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .about-container.two-column {
    grid-template-columns: 1fr;
    width: 95%;
  }
  
  .about-bio, .career-highlights {
    margin-bottom: 1.5rem;
  }
  
  .about-bio:last-child, .career-highlights:last-child {
    margin-bottom: 0;
  }
}
/* Career Section Styling */
#career {
  padding: 3rem 0;
}

#career h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--secondary-color);
  position: relative;
  font-weight: 600;
}

#career h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

.career-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 2.5rem;
  color: var(--secondary-color);
  font-size: 1.05rem;
  line-height: 1.6;
}

.career-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.career-card {
  background-color: #f9f9fc;
  border-left: 4px solid var(--primary-color);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.career-card:hover {
  box-shadow: 0 8px 20px rgba(0,0,0,0.08);
  transform: translateY(-5px);
}

.career-header {
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(0,0,0,0.08);
}

.career-header h3 {
  color: var(--secondary-color);
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.company-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--secondary-color);
}

.company-name {
  font-weight: 500;
  color: var(--primary-color);
}

.duration {
  font-size: 0.9rem;
  opacity: 0.8;
}

.achievements {
  list-style: none;
  padding: 0;
  margin: 0;
}

.achievements li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
  color: var(--secondary-color);
  line-height: 1.5;
}

.achievements li:last-child {
  margin-bottom: 0;
}

.achievements li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--primary-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .career-grid {
    grid-template-columns: 1fr;
  }
  
  .career-card {
    margin-bottom: 1.5rem;
  }
}


/* Section container */
.youtube-section {
  width: 100%;
  padding: 3rem 0;
  box-sizing: border-box;
  background-color: #fcfcfd;
}

.youtube-section .section-title {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--secondary-color);
  position: relative;
  font-weight: 600;
}

.youtube-section .section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

/* Video grid container */
.video-grid-container {
  width: 90%;
  max-width: 1400px;
  margin: 0 auto;
}

/* Update the existing video-grid to work within the container */
.video-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

/* Video card styles */
.video-card {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.video-card:hover {
  transform: translateY(-5px);
}

/* Standardize video and course image dimensions */

/* Video wrapper - set fixed aspect ratio */
.video-wrapper {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
  height: 0;
  overflow: hidden;
  border-radius: 8px; /* Match course image border-radius */
}

.video-wrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

/* Course Image Styles - match video dimensions */
.course-image {
  width: 100%;
  position: relative;
  padding-bottom: 56.25%; /* Same 16:9 aspect ratio as videos */
  height: 0;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 1rem;
  background-color: #f0f0f0;
}

.course-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover; /* Changed from contain to cover for better appearance */
  object-position: center;
}

/* Make cards the same height */
.video-card, .course-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.video-card:hover, .course-card:hover {
  transform: translateY(-5px);
}

/* Standardize info sections */
.video-info, .course-card > *:not(.course-image) {
  padding: 15px;
}

/* Make titles the same size */
.video-title, .course-card h3 {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: #333;
}

/* Make descriptions the same size */
.video-description, .course-description {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 15px;
}

/* Button styles */
.watch-on-youtube {
  display: inline-block;
  background-color: #4a6fa5;
  color: white;
  padding: 8px 15px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.watch-on-youtube:hover {
  background-color: #3a5985;
}

/* Media queries for responsive design */
@media (max-width: 768px) {
  .video-grid {
      grid-template-columns: 1fr; /* One column on mobile */
  }
}
/* Full-width Courses Section */
#courses {
  width: 100%;
  padding: 3rem 0;
  box-sizing: border-box;
  background-color: #fcfcfd;
}

.courses-wrapper {
  width: 90%;
  max-width: 1400px;
  margin: 0 auto;
}

#courses h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--secondary-color);
  position: relative;
  font-weight: 600;
}

#courses h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

.courses-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  width: 100%;
}

.course-card {
  background-color: #f9f9fc;
  border-radius: 10px;
  padding: 1.75rem;
  position: relative;
  transition: all 0.3s ease;
  border-top: 4px solid var(--primary-color);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.course-card:hover {
  box-shadow: 0 10px 25px rgba(0,0,0,0.08);
  transform: translateY(-5px);
}

.course-badge {
  position: absolute;
  top: -12px;
  right: 20px;
  background-color: var(--primary-color);
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  letter-spacing: 0.5px;
}

.course-card h3 {
  color: var(--secondary-color);
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  padding-right: 20px;
  line-height: 1.4;
}

.course-year {
  font-size: 0.9rem;
  color: var(--secondary-color);
  opacity: 0.7;
  margin-bottom: 1rem;
}

.course-description {
  color: var(--secondary-color);
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0;
  flex-grow: 1;
}

/* Course Link Styles */
.course-link {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s ease;
}

.course-link:hover h3 {
  color: var(--primary-color);
}

.course-button {
  display: inline-block;
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  text-decoration: none;
  border-radius: 5px;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  text-align: center;
  align-self: flex-start;
}

.course-button:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .courses-wrapper {
    width: 95%;
  }
  
  .courses-grid {
    grid-template-columns: 1fr;
  }
  
  .course-card {
    margin-bottom: 1.5rem;
  }
  
  .course-card:last-child {
    margin-bottom: 0;
  }
}

/* Course Image Styles */
.course-image {
  width: 100%; /* Ensure full width of the card */
  height: 250px; /* Maintains the existing height */
  margin-bottom: 1rem;
  border-radius: 8px;
  overflow: hidden;
  display: flex; /* Use flexbox to center the image */
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0; /* Optional: add a light background if image has transparency */
}

.course-image img {
  width: 100%; /* Full width of the container */
  height: 100%; /* Full height of the container */
  object-fit: contain; /* Shows entire image without cropping */
  object-position: center; /* Center the image */
}

.course-card:hover .course-image img {
  transform: scale(1.02); /* Subtle zoom, reduced from 1.05 */
  transition: transform 0.3s ease;
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
  .course-image {
    height: 220px; /* Maintained mobile height */
  }
}

/* Course Reviews Styles */
.course-reviews {
  margin: 1.5rem 0;
  width: 100%;
}

.course-reviews h4 {
  font-size: 1.1rem;
  color: var(--secondary-color);
  margin-bottom: 0.75rem;
  font-weight: 500;
  position: relative;
  padding-bottom: 8px;
}

.course-reviews h4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--primary-color);
}

.review-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.review {
  background-color: #f5f5f7;
  border-radius: 8px;
  padding: 1rem;
  position: relative;
}

.review::before {
  content: '"';
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  font-size: 2rem;
  color: rgba(0,0,0,0.1);
  font-family: Georgia, serif;
}

.stars {
  color: #FFD700;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.review-text {
  font-size: 0.9rem;
  line-height: 1.5;
  color: #555;
  font-style: italic;
  margin-bottom: 0.5rem;
}

.reviewer {
  font-size: 0.8rem;
  color: #777;
  text-align: right;
  margin: 0;
  font-weight: 500;
}

/* Adjust course card to accommodate reviews */
.course-card {
  padding-bottom: 1.5rem;
}

.course-button {
  margin-top: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .review-container {
    gap: 0.75rem;
  }
  
  .review {
    padding: 0.75rem;
  }
  
  .review-text {
    font-size: 0.85rem;
  }
}

/* Full-width Career Section */
#career {
  width: 100%;
  padding: 3rem 0;
  box-sizing: border-box;
  background-color: #fcfcfd;
}

.career-wrapper {
  width: 90%;
  max-width: 1400px;
  margin: 0 auto;
}

#career h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--secondary-color);
  position: relative;
  font-weight: 600;
}

#career h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

.career-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 2.5rem;
  color: var(--secondary-color);
  font-size: 1.05rem;
  line-height: 1.6;
}

.career-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  width: 100%;
}

.career-card {
  background-color: #f9f9fc;
  border-left: 4px solid var(--primary-color);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.career-card:hover {
  box-shadow: 0 8px 20px rgba(0,0,0,0.08);
  transform: translateY(-5px);
}

.career-header {
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(0,0,0,0.08);
}

.career-header h3 {
  color: var(--secondary-color);
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.company-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--secondary-color);
}

.company-name {
  font-weight: 500;
  color: var(--primary-color);
}

.duration {
  font-size: 0.9rem;
  opacity: 0.8;
}

.achievements {
  list-style: none;
  padding: 0;
  margin: 0;
}

.achievements li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
  color: var(--secondary-color);
  line-height: 1.5;
}

.achievements li:last-child {
  margin-bottom: 0;
}

.achievements li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--primary-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .career-wrapper {
    width: 95%;
  }
  
  .career-grid {
    grid-template-columns: 1fr;
  }
  
  .career-card {
    margin-bottom: 1.5rem;
  }
  
  .career-card:last-child {
    margin-bottom: 0;
  }
}

/* Social Media Icons */
.nav-social-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.social-icons {
  display: flex;
  gap: 1rem;
  margin-top: 0.75rem;
}

.social-icon {
  color: var(--secondary-color);
  font-size: 1.2rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: rgba(41, 128, 185, 0.1);
}

/* Deeper colors for social icons by default */
.social-icon.linkedin {
  color: #0a66c2; /* Deeper LinkedIn blue */
}

.social-icon.youtube {
  color: #c4302b; /* Deeper YouTube red */
}

.social-icon.facebook {
  color: #1877f2; /* Facebook blue */
}

/* Even more intense on hover */
.social-icon:hover {
  background-color: rgba(41, 128, 185, 0.2);
}

.social-icon.linkedin:hover {
  color: #004182; /* Darker LinkedIn blue */
}

.social-icon.youtube:hover {
  color: #990000; /* Darker YouTube red */
}

.social-icon.facebook:hover {
  color: #0d5ab9; /* Darker Facebook blue */
}

/* Update responsive design for social icons */
@media (max-width: 900px) {
  .nav-social-container {
    align-items: center;
  }
  
  .social-icons {
    margin-top: 1rem;
  }
}

@media (max-width: 768px) {
  .headline h1 {
    font-size: 3.5rem; /* Slightly smaller on mobile devices */
  }
}

/* About section styling enhancements */
.about-intro {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 1.2rem;
  font-weight: 500;
  color: white; /* Change from var(--secondary-color) to white */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* Add subtle text shadow for better readability */
}

.name-highlight {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  color: #ffffff; /* Brighter white for the name */
  position: relative;
  display: inline-block;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); /* Slightly stronger shadow for the name */
}

.about-text p {
  margin-bottom: 1rem;
  line-height: 1.7;
  color: white; /* Ensure all text is white for better contrast against blue background */
}

/* Projects & GitHub Activity Section - Fixed to match YouTube section */
#projects {
  width: 100%;
  padding: 3rem 0;
  box-sizing: border-box;
  background-color: #fcfcfd;
}

.projects-wrapper {
  width: 90%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 0;
}

#projects .section-title {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--secondary-color);
  position: relative;
  font-weight: 600;
}

#projects .section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

/* Projects Grid - Explicitly set to match YouTube video grid */
.projects-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 3rem;
  width: 100%;
}

/* Override any conflicting styles */
.projects-grid {
  grid-template-columns: repeat(2, 1fr) !important;
}

.project-card {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.project-card:hover {
  transform: translateY(-5px);
}

.project-header {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.project-header h3 {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: #333;
}

.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.project-tag {
  background-color: #f0f0f5;
  color: var(--primary-color);
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
}

.project-description {
  padding: 15px;
  margin-bottom: 0;
  color: #666;
  font-size: 0.9rem;
  flex-grow: 1;
  line-height: 1.6;
}

.project-links {
  display: flex;
  gap: 10px;
  padding: 0 15px 15px;
}

.project-link {
  display: inline-flex;
  align-items: center;
  padding: 8px 15px;
  background-color: #4a6fa5;
  color: white;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.3s ease;
  font-size: 0.9rem;
}

.project-link:hover {
  background-color: #3a5985;
}

.project-link i {
  margin-right: 0.5rem;
}

.demo-link {
  background-color: #6c757d;
}

.demo-link:hover {
  background-color: #5a6268;
}

/* Media queries to match YouTube section */
@media (max-width: 768px) {
  .projects-grid {
    grid-template-columns: 1fr !important;
  }
  
  .projects-wrapper {
    width: 95%;
  }
}

/* GitHub Activity Styling - Updated to match video cards */
.github-activity {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  padding: 2rem;
  margin-top: 2rem;
  transition: transform 0.3s ease;
  width: 100%;
  grid-column: 1 / -1; /* Make it span all columns */
}

.github-calendar {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 1.5rem;
}

.github-chart {
  width: 100%;
  max-width: 100%;
  display: block;
}

.github-stats {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.github-stats iframe {
  max-width: 100%;
}

/* Projects section layout adjustment */
.projects-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 2rem;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

@media (max-width: 768px) {
  .github-activity {
    padding: 1.5rem;
  }
  
  .github-stats {
    flex-direction: column;
    align-items: center;
  }
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .about-container.three-column {
    grid-template-columns: 1fr 1fr;
  }
  
  .career-highlights {
    grid-column: span 2;
    margin-top: 2rem;
  }
  
  .about-container.three-column .about-bio::after {
    content: '';
  }
  
  .about-container.three-column .tech-stack::after {
    content: none;
  }
}

@media (max-width: 768px) {
  .about-container.three-column {
    grid-template-columns: 1fr;
  }
  
  .career-highlights {
    grid-column: span 1;
  }
  
  .about-container.three-column .about-bio::after {
    content: none;
  }
}

/* Three-column About Section with modern design */
#about {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  padding: 3rem 2rem;
  position: relative;
  border-radius: 12px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  margin: 2rem auto;
}

.about-container.three-column {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Remove vertical dividers and use subtle shadows instead */
.about-container.three-column .about-bio::after,
.about-container.three-column .tech-stack::after {
  content: none;
  display: none;
}

/* Style each column as a card */
.about-bio, .tech-stack, .career-highlights {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.about-bio:hover, .tech-stack:hover, .career-highlights:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Section titles with modern underline */
.section-title {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
  position: relative;
  padding-bottom: 0.5rem;
  color: white;
  font-family: 'Montserrat', sans-serif;
  letter-spacing: 0.5px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 3px;
}

/* Improve text readability */
.about-text p {
  font-size: 1rem;
  line-height: 1.7;
  margin-bottom: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  letter-spacing: 0.2px;
}

.about-intro {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1.2rem;
  font-weight: 500;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.name-highlight {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  color: #ffffff;
  position: relative;
  display: inline-block;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Tech Stack styling */
.badge-category {
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.badge-category h4 {
  font-size: 1.1rem;
  margin-bottom: 0.8rem;
  color: white;
  font-weight: 600;
}

/* Career Highlights in About Section */
.career-highlights {
  display: flex;
  flex-direction: column;
  position: relative;
}

.career-highlights .section-title {
  margin-bottom: 1.5rem;
  color: white;
}

.career-card.compact {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.career-card.compact:hover {
  transform: translateY(-3px);
  background-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.career-card.compact .career-header h3 {
  font-size: 1rem;
  margin-bottom: 0.3rem;
  color: white;
}

.career-card.compact .company-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  margin-bottom: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
}

.career-card.compact .company-name {
  font-weight: 600;
  color: #ffffff; /* Brighter white for better visibility */
  background: linear-gradient(90deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-shadow: 0 0 10px rgba(255,255,255,0.3);
  letter-spacing: 0.5px;
  position: relative;
  padding-left: 0.5rem;
}

.career-card.compact .company-name::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 80%;
  background: var(--primary-color);
  border-radius: 2px;
}

.career-card.compact .achievements {
  padding-left: 1rem;
  margin-top: 0.5rem;
}

.career-card.compact .achievements li {
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
  position: relative;
  padding-left: 1rem;
  line-height: 1.4;
}

.career-card.compact .achievements li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: var(--primary-color);
}

/* Enhanced Footer with Social Sharing */
footer {
  background-color: #2c3e50;
  color: white;
  padding: 0;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  padding: 3rem 2rem;
}

.footer-info {
  display: flex;
  flex-direction: column;
}

.footer-logo {
  font-family: 'Dancing Script', cursive;
  font-size: 2rem;
  margin-bottom: 1rem;
  color: white;
}

.footer-info p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.footer-social h4, .footer-share h4 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.social-icons-footer {
  display: flex;
  gap: 1rem;
}

.social-icons-footer .social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 50%;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-icons-footer .social-icon:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

.social-icons-footer .linkedin:hover {
  background-color: #0077b5;
}

.social-icons-footer .youtube:hover {
  background-color: #ff0000;
}

.social-icons-footer .facebook:hover {
  background-color: #1877f2;
}

.social-icons-footer .github:hover {
  background-color: #333;
}

.share-buttons {
  display: flex;
  gap: 1rem;
}

.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 50%;
  transition: all 0.3s ease;
  text-decoration: none;
  cursor: pointer;
}

.share-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

.share-btn.facebook:hover {
  background-color: #1877f2;
}

.share-btn.twitter:hover {
  background-color: #1da1f2;
}

.share-btn.linkedin:hover {
  background-color: #0077b5;
}

.share-btn.email:hover {
  background-color: #ea4335;
}

.footer-bottom {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 1rem;
  text-align: center;
}

.footer-bottom p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .social-icons-footer, .share-buttons {
    justify-content: center;
  }
}

/* Skills at a glance section */
.skills-glance {
  background-color: #f8f9fa;
  padding: 1.5rem 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.skills-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.skills-title h2 {
  font-size: 1.5rem;
  color: var(--secondary-color);
  margin-bottom: 1rem;
  text-align: center;
  font-weight: 600;
  position: relative;
}

.skills-title h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: 2px;
}

.skills-badges {
  display: grid;
  grid-template-columns: repeat(8, auto);
  gap: 1rem;
  margin-top: 1rem;
  max-width: 1200px;
  justify-content: center;
}

@media (max-width: 1200px) {
  .skills-badges {
    grid-template-columns: repeat(4, auto);
  }
}

@media (max-width: 768px) {
  .skills-badges {
    grid-template-columns: repeat(3, auto);
  }
}

@media (max-width: 480px) {
  .skills-badges {
    grid-template-columns: repeat(2, auto);
  }
}

.skill-badge {
  display: flex;
  align-items: center;
  background: white;
  padding: 0.6rem 1.2rem;
  border-radius: 30px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin-bottom: 0.5rem; /* Add bottom margin for better spacing between rows */
}

.skill-badge:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #f8f9fa, white);
}

.skill-badge i {
  color: var(--primary-color);
  font-size: 1.1rem;
  margin-right: 0.5rem;
}

.skill-badge span {
  font-weight: 500;
  color: var(--secondary-color);
  font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .skills-badges {
    gap: 0.8rem;
  }
  
  .skill-badge {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }
}

/* Dividend Income Section */
#dividend-income {
  width: 100%;
  padding: 3rem 0;
  box-sizing: border-box;
  background-color: #fcfcfd;
}

.dividend-wrapper {
  width: 90%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 0;
}

#dividend-income .section-title {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--secondary-color);
  position: relative;
  font-weight: 600;
}

#dividend-income .section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

.dividend-container {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  margin-top: 2rem;
  align-items: center;
}

.dividend-info {
  flex: 1;
  min-width: 300px;
}

.dividend-intro {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: #333;
}

.dividend-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  flex: 1;
  margin: 0 0.5rem;
}

.stat-value {
  display: block;
  font-size: 1.8rem;
  font-weight: 700;
  color: #0056b3;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
}

.dividend-chart {
  flex: 1;
  min-width: 300px;
  height: 350px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  padding: 1.5rem;
}

/* Enhanced dividend stats with target income */
.income-stat {
  display: flex;
  flex-direction: column;
  padding: 1rem;
}

.income-values {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.current-income, .target-income {
  text-align: center;
  flex: 1;
}

.current-income .stat-value {
  color: #0056b3;
}

.target-income .stat-value {
  color: #28a745;
}

.income-progress {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto;
}

.progress-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .income-stat {
    flex-direction: column;
  }
  
  .income-values {
    margin-bottom: 1rem;
  }
  
  .income-progress {
    width: 60px;
    height: 60px;
  }
  
  .progress-percentage {
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .dividend-container {
    flex-direction: column;
  }
  
  .dividend-stats {
    flex-direction: column;
    gap: 1rem;
  }
  
  .stat-item {
    margin: 0;
  }
}

/* Hover Card Styles */
.strategy-container {
  position: relative;
  display: inline-block;
}

.hover-card {
  position: absolute;
  top: calc(100% + 10px);
  left: 0;
  width: 400px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: opacity 0.3s, visibility 0.3s, transform 0.3s;
  z-index: 100;
  pointer-events: none;
}

.strategy-container:hover .hover-card,
.hover-card:hover {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto;
}

/* Active state for JavaScript enhancement */
.hover-card.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  pointer-events: auto;
}

.card-content {
  padding: 20px;
}

.card-content h3 {
  color: var(--secondary-color);
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.2rem;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 8px;
}

.card-content p {
  margin-bottom: 15px;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #444;
}

.card-content ul {
  padding-left: 20px;
  margin-bottom: 15px;
}

.card-content li {
  margin-bottom: 8px;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Add a small arrow/triangle pointing to the link */
.hover-card::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 20px;
  width: 16px;
  height: 16px;
  background-color: white;
  transform: rotate(45deg);
  box-shadow: -3px -3px 5px rgba(0, 0, 0, 0.04);
}

/* Make sure the card stays open when hovering over it */
.hover-card:hover {
  opacity: 1;
  visibility: visible;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hover-card {
    width: 300px;
    left: 50%;
    transform: translateX(-50%) translateY(10px);
  }
  
  .strategy-container:hover .hover-card,
  .hover-card:hover {
    transform: translateX(-50%) translateY(0);
  }
  
  .hover-card::before {
    left: 50%;
    margin-left: -8px;
  }
}

/* Blog Section Styles */
.blog-section {
  padding: 1rem 0 2rem; /* Reduced top padding from 2rem to 1rem */
  background-color: #fcfcfd;
}

.blog-section .container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding-top: 0.5rem; /* Added small top padding to container */
}

.blog-section .section-title {
  text-align: center;
  margin-top: 0.5rem; /* Added small top margin */
  margin-bottom: 1.5rem; /* Reduced from 2rem to 1.5rem */
  color: var(--secondary-color);
  position: relative;
  font-weight: 600;
}

.blog-section .section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.blog-card {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.blog-image {
  height: 200px;
  overflow: hidden;
}

.blog-image a {
  display: block;
  height: 100%;
  overflow: hidden;
}

.blog-image a img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.blog-card:hover .blog-image a img {
  transform: scale(1.05);
}

.blog-content {
  padding: 1.5rem;
}

.blog-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-size: 0.85rem;
  color: #777;
}

.blog-date, .blog-read-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.blog-title {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--secondary-color);
  line-height: 1.4;
}

.blog-excerpt {
  color: #555;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

/* Blog Tags Styling */
.blog-tags {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.blog-tag {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.blog-tag.ai {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.blog-tag.tutorial {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.blog-tag.education {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.blog-tag.testing {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.blog-tag.automation {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.blog-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.read-more-link {
  display: inline-flex;
  align-items: center;
  color: var(--primary-color);
  font-weight: 600;
  text-decoration: none;
  gap: 0.5rem;
  transition: gap 0.3s ease;
}

.read-more-link:hover {
  gap: 0.8rem;
}

.read-more-link i {
  font-size: 0.8rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .blog-grid {
    grid-template-columns: 1fr;
  }
}

/* YouTube thumbnail styling */
.youtube-thumbnail {
  margin: 1rem 0;
}

.youtube-link {
  display: block;
  text-decoration: none;
  color: inherit;
  transition: transform 0.3s ease;
}

.youtube-link:hover {
  transform: translateY(-3px);
}

.thumbnail-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.thumbnail-container img {
  width: 100%;
  display: block;
  transition: transform 0.5s ease;
}

.youtube-link:hover .thumbnail-container img {
  transform: scale(1.05);
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 0, 0, 0.8);
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.play-button i {
  font-size: 30px;
}

.youtube-link:hover .play-button {
  background-color: rgb(255, 0, 0);
  width: 65px;
  height: 65px;
}

.watch-text {
  display: block;
  text-align: center;
  margin-top: 0.5rem;
  font-weight: 500;
  color: var(--primary-color);
  font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .play-button {
    width: 50px;
    height: 50px;
  }
  
  .play-button i {
    font-size: 25px;
  }
  
  .youtube-link:hover .play-button {
    width: 55px;
    height: 55px;
  }
}
