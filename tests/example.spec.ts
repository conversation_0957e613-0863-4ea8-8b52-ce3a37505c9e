import { test, expect } from '@playwright/test';



test('Mission 7 stays hidden permanently via Mission 9 + localStorage', async ({ page }) => {
  await page.goto('http://127.0.0.1:5500/automation/playwright-selenium-cypress-practice.html');

  const mission9 = page.locator('#persistent-hide-btn');
  const mission7 = page.getByRole('heading', {
    level: 2,
    name: 'Mission 7: Search Functionality',
  });

  // Click Mission 9 → this should hide Mission 7 and update localStorage
  await mission9.click();

  // Mission 7 should now be hidden
  await expect(mission7).toBeHidden();

  // Wait until localStorage becomes "true"
  await expect.poll(() =>
    page.evaluate(() => localStorage.getItem('mission7PersistentHide'))
  ).toBe('true');
})    