
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON> - <PERSON>folio</title>

  <link rel="stylesheet" href="chatbot.css">

  
  <!-- Open Graph / Social Media Meta Tags -->
  <meta property="og:title" content="<PERSON><PERSON> - QA Engineer & Automation Specialist">
  <meta property="og:description" content="<PERSON><PERSON><PERSON> of <PERSON><PERSON> Hasan, a QA Engineer specializing in automation testing, CI/CD, and AI-driven testing strategies.">
  <meta property="og:image" content="https://faruk-hasan.com/og-image.jpg">
  <meta property="og:url" content="https://faruk-hasan.com">
  <meta property="og:type" content="website">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="<PERSON><PERSON> - QA Engineer & Automation Specialist">
  <meta name="twitter:description" content="Portfo<PERSON> of <PERSON><PERSON> Hasan, a QA Engineer specializing in automation testing, CI/CD, and AI-driven testing strategies.">
  <meta name="twitter:image" content="https://faruk-hasan.com/og-image.jpg">
  
  <link rel="stylesheet" href="styles.css">
  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png">
  <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">
  <link rel="manifest" href="site.webmanifest">
  <!-- Add Font Awesome for social media icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Add Google Fonts for signature style font -->
  <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@700&display=swap" rel="stylesheet">
  <!-- Add Montserrat font for professional styling -->
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <header>
    <div class="header-content">
      <div class="header-left">
        <div class="profile-image">
          <img src="me.jpg" alt="Faruk Hasan">
        </div>
        <div class="headline">
          <h1>Faruk Hasan</h1>
          <p><span class="highlight">Software QA Engineer | Automation & AI-Driven Testing Specialist</span></p>
        </div>
      </div>
      <div class="nav-social-container">
        <nav>
          <ul>
            <li><a href="#about">About Me</a></li>
            <li><a href="#courses">Courses</a></li>
            <li><a href="#projects">Projects</a></li>
            <li><a href="#blog">Blog</a></li>
            <li><a href="tutoring.html">Tutoring</a></li>
            <li><a href="#my dividends">Dividends</a></li>
            <li><a href="resources.html">Resources</a></li>
          </ul>
        </nav>
        <div class="social-icons">
          <a href="https://www.linkedin.com/in/md-faruk-hasan/" class="social-icon linkedin" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
          <a href="https://www.youtube.com/@kidz_code" class="social-icon youtube" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
          <a href="https://www.facebook.com/HasanMd2020/" class="social-icon facebook" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
        </div>
      </div>
    </div>
    <!-- //test -->
    <!-- <div class="ai-image-link">
      <a href="ai.png" target="_blank">
        <img src="ai.png" alt="AI Technology Illustration" class="thumbnail">
        <span>View AI Illustration <i class="fas fa-external-link-alt"></i></span>
      </a>
    </div> -->
  </header>

  <!-- Skills at a glance section -->
  <section class="skills-glance">
    <div class="skills-container">
      <div class="skills-title">
        <h2>Skills at a Glance</h2>
      </div>
      <div class="skills-badges">
        <!-- First row -->
        <div class="skill-badge">
          <i class="fas fa-code"></i>
          <span>Playwright</span>
        </div>
        <div class="skill-badge">
          <i class="fab fa-python"></i>
          <span>Python</span>
        </div>
        <div class="skill-badge">
          <i class="fab fa-js"></i>
          <span>TypeScript</span>
        </div>
        <div class="skill-badge">
          <i class="fas fa-robot"></i>
          <span>Test Automation</span>
        </div>
        <div class="skill-badge">
          <i class="fab fa-github-alt"></i>
          <span>CI/CD</span>
        </div>
        <div class="skill-badge">
          <i class="fas fa-brain"></i>
          <span>AI Testing</span>
        </div>
        <div class="skill-badge">
          <i class="fab fa-java"></i>
          <span>Java</span>
        </div>
        <div class="skill-badge">
          <i class="fas fa-certificate"></i>
          <span>Playwright MCP</span>
        </div>
        
        <!-- Second row -->
        <div class="skill-badge">
          <i class="fas fa-database"></i>
          <span>SQL</span>
        </div>
        <div class="skill-badge">
          <i class="fab fa-docker"></i>
          <span>Docker</span>
        </div>
        <div class="skill-badge">
          <i class="fab fa-aws"></i>
          <span>AWS</span>
        </div>
        <div class="skill-badge">
          <i class="fab fa-jenkins"></i>
          <span>Jenkins</span>
        </div>
        <div class="skill-badge">
          <i class="fab fa-html5"></i>
          <span>HTML/CSS</span>
        </div>
        <div class="skill-badge">
          <i class="fab fa-git-alt"></i>
          <span>Git</span>
        </div>
        <div class="skill-badge">
          <i class="fab fa-github"></i>
          <span>GitHub Actions</span>
        </div>
        <div class="skill-badge">
          <i class="fab fa-react"></i>
          <span>React Basics</span>
        </div>
      </div>
    </div>
  </section>

  <!-- Blog section moved to appear after Skills section -->
  <section id="blog" class="blog-section">
    <div class="container">
      <h2 class="section-title">Latest Blog Posts</h2>
      <div class="blog-grid">
        <!-- Blog Post 1 - Playwright Fixtures (Latest) -->
        <div class="blog-card">
          <div class="blog-image">
            <a href="blog_post/playwright_fixtures.html">
              <img src="blog_images/playwright_fixtures.png" alt="Playwright Fixtures Tutorial" loading="lazy">
            </a>
          </div>
          <div class="blog-content">
            <div class="blog-meta">
              <div class="blog-date">
                <i class="far fa-calendar-alt"></i>
                <span>September 7, 2025</span>
              </div>
              <div class="blog-read-time">
                <i class="far fa-clock"></i>
                <span>6 min read</span>
              </div>
            </div>
            <h3 class="blog-title">Mastering Playwright Fixtures: Built-in vs Custom</h3>
            <p class="blog-excerpt">Learn how to use Playwright fixtures to simplify test setup and avoid boilerplate code. Complete guide with TypeScript examples, custom fixtures, and best practices for test automation.</p>

            <!-- YouTube thumbnail -->
            <div class="youtube-thumbnail">
              <a href="https://youtu.be/tkjPe86JpGk" target="_blank" class="youtube-link">
                <div class="thumbnail-container">
                  <img src="https://img.youtube.com/vi/tkjPe86JpGk/0.jpg" alt="Watch video tutorial" loading="lazy">
                  <div class="play-button">
                    <i class="fab fa-youtube"></i>
                  </div>
                </div>
                <span class="watch-text">Watch video tutorial</span>
              </a>
            </div>

            <a href="blog_post/playwright_fixtures.html" class="read-more-link">Read More <i class="fas fa-arrow-right"></i></a>
          </div>
        </div>

        <!-- Blog Post 2 -->
        <div class="blog-card">
          <div class="blog-image">
            <a href="blog_post/monocart_report_playwright.html">
              <img src="blog_images/monocart-reporter.jpg" alt="Monocart Playwright Report" loading="lazy">
            </a>
          </div>
          <div class="blog-content">
            <div class="blog-meta">
              <div class="blog-date">
                <i class="far fa-calendar-alt"></i>
                <span>June 10, 2024</span>
              </div>
              <div class="blog-read-time">
                <i class="far fa-clock"></i>
                <span>8 min read</span>
              </div>
            </div>
            <h3 class="blog-title">Integrating Monocart Reporter with Playwright Tests</h3>
            <p class="blog-excerpt">Learn how to enhance your Playwright test reports with Monocart, a powerful visual reporting tool that makes test results more accessible to stakeholders.</p>

            <!-- YouTube thumbnail -->
            <div class="youtube-thumbnail">
              <a href="https://youtu.be/8Ae-lkFAG5Q" target="_blank" class="youtube-link">
                <div class="thumbnail-container">
                  <img src="https://img.youtube.com/vi/8Ae-lkFAG5Q/maxresdefault.jpg" alt="Watch video tutorial" loading="lazy">
                  <div class="play-button">
                    <i class="fab fa-youtube"></i>
                  </div>
                </div>
                <span class="watch-text">Watch video tutorial</span>
              </a>
            </div>

            <a href="blog_post/monocart_report_playwright.html" class="read-more-link">Read More <i class="fas fa-arrow-right"></i></a>
          </div>
        </div>
        
        <!-- Blog Post 3 -->
        <div class="blog-card">
          <div class="blog-image">
            <a href="blog_post/github_actions_playwright_test.html">
              <img src="blog_images/github-actions-playwright.jpg" alt="GitHub Actions CI/CD" loading="lazy">
            </a>
          </div>
          <div class="blog-content">
            <div class="blog-meta">
              <div class="blog-date">
                <i class="far fa-calendar-alt"></i>
                <span>June 20, 2024</span>
              </div>
              <div class="blog-read-time">
                <i class="far fa-clock"></i>
                <span>12 min read</span>
              </div>
            </div>
            <h3 class="blog-title">Setting Up Playwright Tests in Your CI Pipeline with GitHub Actions</h3>
            <p class="blog-excerpt">A step-by-step guide to configuring GitHub Actions for running Playwright tests automatically on every push and pull request.</p>

            <!-- YouTube thumbnail -->
            <div class="youtube-thumbnail">
              <a href="https://youtu.be/J3XbKWAFvC0" target="_blank" class="youtube-link">
                <div class="thumbnail-container">
                  <img src="https://img.youtube.com/vi/J3XbKWAFvC0/maxresdefault.jpg" alt="Watch video tutorial" loading="lazy">
                  <div class="play-button">
                    <i class="fab fa-youtube"></i>
                  </div>
                </div>
                <span class="watch-text">Watch video tutorial</span>
              </a>
            </div>

            <a href="blog_post/github_actions_playwright_test.html" class="read-more-link">Read More <i class="fas fa-arrow-right"></i></a>
          </div>
        </div>

      </div>
    </div>
  </section>

  <section id="about">
    <div class="about-container two-column">
      <!-- Bio Column -->
      <div class="about-bio">
        <h2 class="section-title">About Me</h2>
        <div class="about-content">
          <div class="about-text">
            <p class="about-intro">Hi, I'm <span class="name-highlight">Faruk Hasan</span> — a QA Engineer, educator, and AI enthusiast with over 5 years of experience in automation testing, CI/CD, and cloud infrastructure.</p>
            
            <p>I specialize in building advanced testing frameworks using Playwright with TypeScript and Python, ensuring efficient and reliable software delivery. I also design and teach coding and AI courses for middle and high school students, blending real-world tech skills with hands-on learning.</p>
            
            <p>Currently, I'm exploring AI-driven QA tools to enhance test reliability, identify flaky tests, and optimize automation strategies.</p>
          </div>
        </div>
      </div>
      
      <!-- Career Highlights Column -->
      <div class="career-highlights">
        <h2 class="section-title">Career Highlights</h2>
        
        <div class="career-card compact">
          <div class="career-header">
            <h3>Senior Software QA Engineer</h3>
            <div class="company-details">
              <span class="company-name">Digital.ai</span>
              <span class="duration">2022 – Present</span>
            </div>
          </div>
          <ul class="achievements">
            <li>Design and implement Playwright test scripts, increasing test coverage and reducing manual efforts.</li>
            <li>Streamline regression testing, reducing cycle time by 50%.</li>
            <li>Integrate GitHub Actions for automated test execution, improving defect identification speed.</li>
            <li>Lead cross-functional testing initiatives for enterprise-level applications.</li>
          </ul>
        </div>

        <div class="career-card compact">
          <div class="career-header">
            <h3>Software Test Engineer</h3>
            <div class="company-details">
              <span class="company-name">M.M.Hayes Co</span>
              <span class="duration">2020 – 2022</span>
            </div>
          </div>
          <ul class="achievements">
            <li>Developed Selenium-based frameworks, boosting test efficiency by 30%.</li>
            <li>Trained junior QA engineers, improving team automation proficiency.</li>
            <li>Reduced regression cycle time by implementing cross-browser testing strategies.</li>
            <li>Collaborated with developers to implement CI/CD pipelines for continuous testing.</li>
          </ul>
        </div>
        
        <a href="javascript:void(0)" onclick="openResume()" class="view-more-link">View Full Career <i class="fas fa-arrow-right"></i></a>
      </div>
    </div>
  </section>

  <!-- Remove the entire Career section -->
  <!-- <section id="career">
    <div class="career-wrapper">
      <h2>Career</h2>
      
      <div class="career-grid">
        <div class="career-card">
          <div class="career-header">
            <h3>Senior Software QA Engineer</h3>
            <div class="company-details">
              <span class="company-name">Digital.ai</span>
              <span class="duration">2022 – Present</span>
            </div>
          </div>
          <ul class="achievements">
            <li>Design and implement Playwright test scripts, increasing test coverage and reducing manual efforts.</li>
            <li>Streamline regression testing, reducing cycle time by 50%.</li>
            <li>Integrate GitHub Actions for automated test execution, improving defect identification speed.</li>
          </ul>
        </div>

        <div class="career-card">
          <div class="career-header">
            <h3>Software Test Engineer</h3>
            <div class="company-details">
              <span class="company-name">M.M.Hayes Co</span>
              <span class="duration">2020 – 2022</span>
            </div>
          </div>
          <ul class="achievements">
            <li>Developed Selenium-based frameworks, boosting test efficiency by 30%.</li>
            <li>Reduced regression cycle time by implementing cross-browser testing strategies.</li>
            <li>Trained junior QA engineers, improving team automation proficiency.</li>
          </ul>
        </div>
      </div>
    </div>
  </section> -->

  <section id="courses">
    <div class="courses-wrapper">
      <h2>Courses</h2>
      <div class="courses-grid">
        <div class="course-card">
          <div class="course-image">
            <img src="udemy_course_image.jpg" alt="Python Fundamentals Course" loading="lazy">
          </div>
          <div class="course-badge">Udemy</div>
          <a href="https://www.udemy.com/share/10bHxx/" target="_blank" class="course-link">
            <h3>Python Fundamentals: Fun and Practical Projects for Beginners</h3>
          </a>
          <div class="course-year">2024</div>
          <p class="course-description">An interactive course focusing on Python basics through real-world projects.</p>
          
          <!-- Reviews Section for first course -->
          <div class="course-reviews">
            <h4>Student Reviews</h4>
            <div class="review-container">
              <div class="review">
                <div class="stars">
                  <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i>
                </div>
                <p class="review-text">"Great course. This is actually fun. Never learned Python but it looks so easy now. Thank You Instructor Mr. Faruk Hasan."</p>
                <p class="reviewer">- Habibus Sobhan S.</p>
              </div>
              <div class="review">
                <div class="stars">
                  <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i>
                </div>
                <p class="review-text">"Really easy to follow and learn!"</p>
                <p class="reviewer">- Syed M.</p>
              </div>
            </div>
          </div>
          
          <a href="https://www.udemy.com/share/10bHxx/" target="_blank" class="course-button">View Course</a>
        </div>
        
        <div class="course-card">
          <div class="course-image">
            <img src="pro_python.png" alt="Advanced Python Development Course" loading="lazy">
          </div>
          <div class="course-badge">Outschool</div>
          <a href="https://outschool.com/classes/python-advanced-coding-playwright-flask-git-html-database-and-api-projects-ESjTt3fz" target="_blank" class="course-link">
            <h3>Advanced Python/Playwright Development</h3>
          </a>
          <div class="course-year">2023</div>
          <p class="course-description">Hands-on learning for advanced Python topics, equipping learners with professional skills.</p>
          <a href="https://outschool.com/classes/python-advanced-coding-playwright-flask-git-html-database-and-api-projects-ESjTt3fz" target="_blank" class="course-button">View Course</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Blog section removed -->

  <section id="projects">
    <div class="projects-wrapper">
      <h2 class="section-title">Projects & GitHub Activity</h2>
      
      <div class="projects-grid">
        <div class="project-card">
          <div class="project-header">
            <h3>Playwright Browser Extension</h3>
            <div class="project-tags">
              <span class="project-tag">TypeScript</span>
              <span class="project-tag">Chrome Extension</span>
              <span class="project-tag">Testing</span>
            </div>
          </div>
          <p class="project-description">A Chrome extension that displays Playwright test results in real-time, providing instant feedback on test execution status.</p>
          <div class="project-links">
            <a href="https://github.com/faruklmu17/playwright_test_result" class="project-link" target="_blank"><i class="fab fa-github"></i> View on GitHub</a>
            <a href="#" class="project-link demo-link" target="_blank"><i class="fas fa-external-link-alt"></i> Live Demo</a>
          </div>
        </div>
        
        <div class="project-card">
          <div class="project-header">
            <h3>AI-Powered Test Generator</h3>
            <div class="project-tags">
              <span class="project-tag">Python</span>
              <span class="project-tag">AI/ML</span>
              <span class="project-tag">Automation</span>
            </div>
          </div>
          <p class="project-description">An intelligent test generator that uses machine learning to analyze application behavior and create optimized test cases automatically.</p>
          <div class="project-links">
            <a href="https://github.com/faruklmu17/ai-test-generator" class="project-link" target="_blank"><i class="fab fa-github"></i> View on GitHub</a>
          </div>
        </div>
      </div>
      
      <!-- GitHub Activity Widget -->
      <div class="github-activity">
        <h3>My GitHub Contributions</h3>
        <div class="github-calendar">
          <!-- GitHub contribution calendar widget -->
          <img src="https://ghchart.rshah.org/faruklmu17" alt="Faruk Hasan's GitHub Contribution Chart" class="github-chart">
        </div>
        <div class="github-stats">
          <script src="https://gist-it.appspot.com/github/faruklmu17/github-stats/blob/main/generated/overview.svg"></script>
          <script src="https://gist-it.appspot.com/github/faruklmu17/github-stats/blob/main/generated/languages.svg"></script>
        </div>
        <a href="https://github.com/faruklmu17" class="view-more-link" target="_blank">View My GitHub Profile <i class="fas fa-arrow-right"></i></a>
      </div>
    </div>
  </section>



  <section id="dividend-income">
    <div class="dividend-wrapper">
      <h2 class="section-title">Dividend Income Portfolio</h2>
      <div class="dividend-container">
        <div class="dividend-info">
          <p class="dividend-intro">My investment strategy focuses on building a sustainable dividend income stream for early retirement. Below is the current allocation of my dividend portfolio.</p>
          <div class="dividend-stats">
            <div class="stat-item">
              <span class="stat-value">5.7%</span>
              <span class="stat-label">Average Yield</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">12%</span>
              <span class="stat-label">Annual Growth</span>
            </div>
            <div class="stat-item income-stat">
              <div class="income-values">
                <div class="current-income">
                  <span class="stat-value" id="current-income">$190</span>
                  <span class="stat-label">Current Monthly</span>
                </div>
                <div class="target-income">
                  <span class="stat-value">$250</span>
                  <span class="stat-label">2025 Target</span>
                </div>
              </div>
              <div class="income-progress">
                <canvas id="incomeProgressChart"></canvas>
                <div class="progress-percentage" id="income-percentage">50%</div>
              </div>
            </div>
          </div>
          <div class="strategy-container">
            <a href="javascript:void(0)" class="view-more-link" id="strategy-trigger">View Investment Strategy <i class="fas fa-arrow-right"></i></a>
            
            <div id="strategy-card" class="hover-card">
              <div class="card-content">
                <h3>My Dividend Investment Strategy</h3>
                <p>My investment approach focuses on building a sustainable dividend income stream for early retirement through:</p>
                <ul>
                  <li><strong>Quality over yield:</strong> Prioritizing companies with sustainable payout ratios and growth potential</li>
                  <li><strong>Sector diversification:</strong> Spreading investments across multiple industries to reduce risk</li>
                  <li><strong>Dividend growth:</strong> Focusing on companies with a history of increasing dividends</li>
                  <li><strong>ETF core:</strong> Using dividend ETFs like SCHD and JEPI as the foundation of the portfolio</li>
                  <li><strong>Reinvestment:</strong> Automatically reinvesting dividends to compound returns</li>
                </ul>
                <p>My goal is to achieve financial independence through a growing passive income stream that eventually covers all living expenses.</p>
              </div>
            </div>
          </div>
        </div>
        <div class="dividend-chart">
          <canvas id="dividendChart"></canvas>
        </div>
      </div>
    </div>
  </section>

  <script>
  function toggleStrategySection() {
    const details = document.getElementById('strategy-details');
    const toggle = document.getElementById('strategy-toggle');
    
    if (details.style.display === 'none') {
      details.style.display = 'block';
      toggle.innerHTML = 'Hide Investment Strategy <i class="fas fa-arrow-up"></i>';
      // Smooth scroll to the details section
      details.scrollIntoView({ behavior: 'smooth' });
    } else {
      details.style.display = 'none';
      toggle.innerHTML = 'View Investment Strategy <i class="fas fa-arrow-right"></i>';
    }
  }
  </script>

<!--   
  <section id="projects" class="container">
    <h2>Projects</h2>
    <ul>
      <li>
        <strong>Playwright Browser Extension</strong>
        <p>A Chrome extension that displays Playwright test results in real-time using TypeScript, JavaScript, and HTML/CSS.</p>
        <a href="https://github.com/faruklmu17/playwright_test_result">GitHub Link</a>
      </li>
    </ul>
  </section>

  



  <section id="travel" class="container">
    <h2>Travel</h2>
    <p>I love exploring new destinations. Here, I share my travel stories and tips.</p>
  </section>

  <section id="investment" class="container">
    <h2>Investment</h2>
    <p>I focus on dividend investing to achieve early retirement. Follow my journey!</p>
  </section>

  <section id="contact" class="container">
    <h2>Contact</h2>
    <form>
      <label for="name">Name:</label>
      <input type="text" id="name" name="name" required>
      <label for="email">Email:</label>
      <input type="email" id="email" name="email" required>
    </form>
  </section> -->

  <footer>
    <div class="footer-content">
      <div class="footer-info">
        <div class="footer-logo">Faruk Hasan</div>
        <p>QA Engineer | Automation & AI-Driven Testing Specialist</p>
      </div>
      
      <div class="footer-social">
        <h4>Connect With Me</h4>
        <div class="social-icons-footer">
          <a href="https://www.linkedin.com/in/md-faruk-hasan/" class="social-icon linkedin" aria-label="LinkedIn" target="_blank"><i class="fab fa-linkedin"></i></a>
          <a href="https://www.youtube.com/@kidz_code" class="social-icon youtube" aria-label="YouTube" target="_blank"><i class="fab fa-youtube"></i></a>
          <a href="https://www.facebook.com/HasanMd2020/" class="social-icon facebook" aria-label="Facebook" target="_blank"><i class="fab fa-facebook-f"></i></a>
          <a href="https://github.com/faruklmu17" class="social-icon github" aria-label="GitHub" target="_blank"><i class="fab fa-github"></i></a>
        </div>
      </div>
      
      <div class="footer-share">
        <h4>Share This Page</h4>
        <div class="share-buttons">
          <a href="javascript:void(0)" onclick="shareOnFacebook()" class="share-btn facebook" aria-label="Share on Facebook"><i class="fab fa-facebook-f"></i></a>
          <a href="javascript:void(0)" onclick="shareOnTwitter()" class="share-btn twitter" aria-label="Share on Twitter"><i class="fab fa-twitter"></i></a>
          <a href="javascript:void(0)" onclick="shareOnLinkedIn()" class="share-btn linkedin" aria-label="Share on LinkedIn"><i class="fab fa-linkedin-in"></i></a>
          <a href="javascript:void(0)" onclick="shareByEmail()" class="share-btn email" aria-label="Share by Email"><i class="fas fa-envelope"></i></a>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <p>&copy; 2025 Faruk Hasan. All rights reserved.</p>
    </div>
  </footer>

  <!-- Enhanced Chatbot Interface -->
  <div class="chatbot-container">
    <!-- Minimized Chatbot Button -->
    <button class="chatbot-toggle" id="chatbot-toggle" aria-label="Open Chat Assistant">
      <svg class="chat-icon" viewBox="0 0 24 24">
        <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
      </svg>
      <div class="notification-badge" id="notification-badge">1</div>
    </button>

    <!-- Expanded Chatbot Window -->
    <div class="chatbot-window" id="chatbot-window">
      <div class="chatbot-header">
        <div class="chatbot-title">
          <div class="chatbot-avatar">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </div>
          <span>AI Assistant</span>
        </div>
        <button class="minimize-btn" id="minimize-btn" aria-label="Minimize Chat">
          <svg class="minimize-icon" viewBox="0 0 24 24">
            <path d="M19 13H5v-2h14v2z"/>
          </svg>
        </button>
      </div>

      <div class="chatbot-loading" id="chatbot-loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading AI Assistant...</div>
      </div>

      <iframe
        class="chatbot-frame"
        id="chatbot-frame"
        src=""
        style="display: none;"
        title="AI Chat Assistant">
      </iframe>
    </div>
  </div>





  
  <script>
    // Social sharing functions
    function shareOnFacebook() {
      const url = encodeURIComponent(window.location.href);
      const text = encodeURIComponent("Check out Faruk Hasan's portfolio - QA Engineer & Automation Testing Specialist");
      window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${text}`, '_blank');
    }
    
    function shareOnTwitter() {
      const url = encodeURIComponent(window.location.href);
      const text = encodeURIComponent("Check out Faruk Hasan's portfolio - QA Engineer & Automation Testing Specialist #QA #Testing #Automation");
      window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
    }
    
    function shareOnLinkedIn() {
      const url = encodeURIComponent(window.location.href);
      const title = encodeURIComponent("Faruk Hasan - QA Engineer & Automation Specialist");
      const summary = encodeURIComponent("Portfolio of Faruk Hasan, a QA Engineer specializing in automation testing, CI/CD, and AI-driven testing strategies.");
      window.open(`https://www.linkedin.com/shareArticle?mini=true&url=${url}&title=${title}&summary=${summary}`, '_blank');
    }
    
    function shareByEmail() {
      const subject = encodeURIComponent("Check out Faruk Hasan's Portfolio");
      const body = encodeURIComponent(`I thought you might be interested in Faruk Hasan's portfolio: ${window.location.href}`);
      window.location.href = `mailto:?subject=${subject}&body=${body}`;
    }
  </script>
</body>
</html>

<script>
  function openResume() {
    // Try multiple possible locations
    const possiblePaths = [
      'resume.pdf',
      'Resume.pdf',
      '/resume.pdf',
      '/Resume.pdf',
      'assets/resume.pdf',
      'docs/resume.pdf',
      'files/resume.pdf'
    ];
    
    // Try to fetch each path to see if the file exists
    Promise.all(possiblePaths.map(path => 
      fetch(path, { method: 'HEAD' })
        .then(response => ({ path, exists: response.ok }))
        .catch(() => ({ path, exists: false }))
    )).then(results => {
      // Find the first path that exists
      const foundPath = results.find(result => result.exists);
      
      if (foundPath) {
        // Open the PDF in a new tab
        window.open(foundPath.path, '_blank');
      } else {
        // Alert the user if no PDF is found
        alert('Resume PDF not found. Please upload your resume file to the website root directory.');
      }
    });
  }
  
  // Add Chart.js and XLSX libraries
  document.addEventListener('DOMContentLoaded', function() {
    // Load Chart.js
    const chartScript = document.createElement('script');
    chartScript.src = 'https://cdn.jsdelivr.net/npm/chart.js';
    
    // Load XLSX library for Excel parsing
    const xlsxScript = document.createElement('script');
    xlsxScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
    
    // When all scripts are loaded, initialize the chart
    Promise.all([
      new Promise(resolve => { chartScript.onload = resolve; }),
      new Promise(resolve => { xlsxScript.onload = resolve; })
    ]).then(() => {
      loadPortfolioData();
    });
    
    document.body.appendChild(chartScript);
    document.body.appendChild(xlsxScript);
  });
  
  function loadPortfolioData() {
    // Fetch the Excel file
    fetch('portfolio_data.xlsx')
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.arrayBuffer();
      })
      .then(data => {
        // Parse the Excel file
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        
        // Process the data for the chart
        processPortfolioData(jsonData);
      })
      .catch(error => {
        console.error('Error loading portfolio data:', error);
        // Fallback to default data if file can't be loaded
        initDividendChartWithDefaultData();
      });
  }
  
  function processPortfolioData(data) {
    // Calculate total market value for each position
    let totalPortfolioValue = 0;
    let totalDividendIncome = 0;
    let totalYield = 0;
    
    // Add market value to each stock
    data.forEach(stock => {
      // Calculate market value (shares * current price)
      stock.marketValue = stock.Shares * stock['Current Price'];
      
      // Calculate annual dividend income
      stock.dividendIncome = stock.marketValue * (stock['Dividend Yield (%)'] / 100);
      
      // Add to totals
      totalPortfolioValue += stock.marketValue;
      totalDividendIncome += stock.dividendIncome;
    });
    
    // Calculate overall portfolio yield
    const portfolioYield = (totalDividendIncome / totalPortfolioValue) * 100;
    
    // Sort stocks by market value (descending)
    data.sort((a, b) => b.marketValue - a.marketValue);
    
    // Group smaller positions as "Other" if they're less than 3% of portfolio
    const threshold = totalPortfolioValue * 0.03;
    const mainPositions = [];
    let otherValue = 0;
    
    data.forEach(stock => {
      if (stock.marketValue >= threshold) {
        mainPositions.push({
          ticker: stock.Stock,
          value: stock.marketValue,
          percentage: (stock.marketValue / totalPortfolioValue) * 100
        });
      } else {
        otherValue += stock.marketValue;
      }
    });
    
    // Add "Other" category if needed
    if (otherValue > 0) {
      mainPositions.push({
        ticker: 'Other',
        value: otherValue,
        percentage: (otherValue / totalPortfolioValue) * 100
      });
    }
    
    // Prepare data for chart
    const labels = mainPositions.map(pos => pos.ticker);
    const values = mainPositions.map(pos => pos.percentage.toFixed(1));
    
    // Initialize chart with the processed data
    initDividendChart(labels, values);
    
    // Update portfolio stats
    updatePortfolioStats(portfolioYield, totalDividendIncome, data);
  }
  
  function updatePortfolioStats(yield, annualIncome, data) {
    // Calculate growth rate (using the difference between current and purchase prices)
    let totalGrowth = 0;
    let totalInvestment = 0;
    
    data.forEach(stock => {
      const investment = stock.Shares * stock['Purchase Price'];
      const currentValue = stock.Shares * stock['Current Price'];
      totalInvestment += investment;
      totalGrowth += (currentValue - investment);
    });
    
    const growthRate = (totalGrowth / totalInvestment) * 100;
    const monthlyIncome = (annualIncome / 12);
    const targetMonthlyIncome = 250; // Target monthly income by end of 2025
    const progressPercentage = (monthlyIncome / targetMonthlyIncome) * 100;
    
    // Update the stats display
    document.querySelector('.dividend-stats .stat-item:nth-child(1) .stat-value').textContent = yield.toFixed(1) + '%';
    document.querySelector('.dividend-stats .stat-item:nth-child(2) .stat-value').textContent = growthRate.toFixed(1) + '%';
    document.getElementById('current-income').textContent = '$' + monthlyIncome.toFixed(0);
    document.getElementById('income-percentage').textContent = progressPercentage.toFixed(0) + '%';
    
    // Create the progress chart
    createIncomeProgressChart(progressPercentage);
  }
  
  function initDividendChart(labels, data) {
    const ctx = document.getElementById('dividendChart').getContext('2d');
    
    // Color palette for the chart
    const colors = [
      '#4285F4', // Blue
      '#34A853', // Green
      '#FBBC05', // Yellow
      '#EA4335', // Red
      '#5F6368', // Grey
      '#1A73E8', // Light Blue
      '#188038', // Dark Green
      '#F29900', // Orange
      '#D93025', // Dark Red
      '#9AA0A6', // Light Grey
      '#185ABC', // Dark Blue
      '#137333', // Forest Green
      '#E37400'  // Dark Orange
    ];
    
    // Chart configuration
    const config = {
      type: 'pie',
      data: {
        labels: labels,
        datasets: [{
          data: data,
          backgroundColor: colors.slice(0, labels.length),
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'right',
            labels: {
              font: {
                family: 'Montserrat',
                size: 12
              }
            }
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = context.raw || 0;
                return `${label}: ${value}%`;
              }
            }
          }
        }
      }
    };
    
    // Create the chart
    new Chart(ctx, config);
  }
  
  function createIncomeProgressChart(percentage) {
    const ctx = document.getElementById('incomeProgressChart').getContext('2d');
    
    // Destroy previous chart if it exists
    if (window.incomeChart) {
      window.incomeChart.destroy();
    }
    
    // Create new chart
    window.incomeChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        datasets: [{
          data: [percentage, 100 - percentage],
          backgroundColor: [
            '#28a745', // Green for progress
            '#e9ecef'  // Light gray for remaining
          ],
          borderWidth: 0
        }]
      },
      options: {
        cutout: '70%',
        responsive: true,
        maintainAspectRatio: true,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            enabled: false
          }
        },
        animation: {
          animateRotate: true,
          animateScale: true
        }
      }
    });
  }

  function initDividendChartWithDefaultData() {
    // Default data if file loading fails
    const labels = ['SCHD', 'JEPI', 'T', 'SPYD', 'O', 'PFE', 'XYLD', 'Other'];
    const data = [25, 20, 15, 10, 10, 8, 7, 5];
    
    initDividendChart(labels, data);
    
    // Set default stats
    document.querySelector('.dividend-stats .stat-item:nth-child(1) .stat-value').textContent = '5.7%';
    document.querySelector('.dividend-stats .stat-item:nth-child(2) .stat-value').textContent = '12%';
    document.getElementById('current-income').textContent = '$190';
    document.getElementById('income-percentage').textContent = '76%';

    // Create default progress chart
    createIncomeProgressChart(76);
  }
</script>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const strategyTrigger = document.getElementById('strategy-trigger');
    const strategyCard = document.getElementById('strategy-card');
    
    // Add a small delay before closing to prevent accidental closures
    let timeout;
    
    strategyTrigger.addEventListener('mouseenter', function() {
      clearTimeout(timeout);
      strategyCard.classList.add('active');
    });
    
    strategyTrigger.addEventListener('mouseleave', function(e) {
      // Check if the mouse is moving toward the card
      const rect = strategyCard.getBoundingClientRect();
      const isMovingToward = 
        e.clientX >= rect.left - 20 && 
        e.clientX <= rect.right + 20 && 
        e.clientY >= rect.top - 20;
      
      if (!isMovingToward) {
        timeout = setTimeout(() => {
          if (!strategyCard.matches(':hover')) {
            strategyCard.classList.remove('active');
          }
        }, 300);
      }
    });
    
    strategyCard.addEventListener('mouseenter', function() {
      clearTimeout(timeout);
    });
    
    strategyCard.addEventListener('mouseleave', function() {
      timeout = setTimeout(() => {
        strategyCard.classList.remove('active');
      }, 300);
    });
  });
</script>

<!-- Enhanced Chatbot JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const chatbotToggle = document.getElementById('chatbot-toggle');
    const chatbotWindow = document.getElementById('chatbot-window');
    const minimizeBtn = document.getElementById('minimize-btn');
    const chatbotFrame = document.getElementById('chatbot-frame');
    const chatbotLoading = document.getElementById('chatbot-loading');
    const notificationBadge = document.getElementById('notification-badge');

    let isOpen = false;
    let isLoaded = false;

    // Chatbot URLs
    const chatbotUrl = 'https://hasanfaruk25-personal-chatbot.hf.space/?__theme=light';

    // Initialize chatbot state
    function initializeChatbot() {
        // Hide notification badge after first interaction
        if (notificationBadge) {
            setTimeout(() => {
                notificationBadge.style.display = 'none';
            }, 3000);
        }

        // Add welcome animation to toggle button
        setTimeout(() => {
            chatbotToggle.style.animation = 'pulse 2s ease-in-out 3';
        }, 2000);
    }

    // Load chatbot iframe
    function loadChatbot() {
        if (!isLoaded) {
            chatbotFrame.src = chatbotUrl;
            isLoaded = true;

            // Handle iframe load
            chatbotFrame.onload = function() {
                setTimeout(() => {
                    chatbotLoading.style.display = 'none';
                    chatbotFrame.style.display = 'block';
                }, 1000);
            };

            // Fallback in case onload doesn't fire
            setTimeout(() => {
                chatbotLoading.style.display = 'none';
                chatbotFrame.style.display = 'block';
            }, 3000);
        } else {
            chatbotLoading.style.display = 'none';
            chatbotFrame.style.display = 'block';
        }
    }

    // Open chatbot
    function openChatbot() {
        if (!isOpen) {
            isOpen = true;
            chatbotWindow.classList.add('active');
            chatbotToggle.style.transform = 'scale(0.9)';

            // Hide notification badge
            if (notificationBadge) {
                notificationBadge.style.display = 'none';
            }

            // Load chatbot content
            loadChatbot();

            // Add body class to prevent scrolling on mobile
            document.body.style.overflow = window.innerWidth <= 768 ? 'hidden' : 'auto';
        }
    }

    // Close chatbot
    function closeChatbot() {
        if (isOpen) {
            isOpen = false;
            chatbotWindow.classList.remove('active');
            chatbotToggle.style.transform = 'scale(1)';

            // Reset loading state for next open
            if (isLoaded) {
                chatbotLoading.style.display = 'none';
                chatbotFrame.style.display = 'block';
            } else {
                chatbotLoading.style.display = 'flex';
                chatbotFrame.style.display = 'none';
            }

            // Restore body scrolling
            document.body.style.overflow = 'auto';
        }
    }

    // Toggle chatbot
    function toggleChatbot() {
        if (isOpen) {
            closeChatbot();
        } else {
            openChatbot();
        }
    }

    // Event listeners
    chatbotToggle.addEventListener('click', toggleChatbot);
    minimizeBtn.addEventListener('click', closeChatbot);

    // Close chatbot when clicking outside (optional)
    document.addEventListener('click', function(event) {
        if (isOpen && !chatbotWindow.contains(event.target) && !chatbotToggle.contains(event.target)) {
            // Uncomment the line below if you want to close on outside click
            // closeChatbot();
        }
    });

    // Handle escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && isOpen) {
            closeChatbot();
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (isOpen && window.innerWidth <= 768) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'auto';
        }
    });

    // Initialize
    initializeChatbot();

    // Add some interactive feedback
    chatbotToggle.addEventListener('mouseenter', function() {
        if (!isOpen) {
            this.style.transform = 'translateY(-2px) scale(1.05)';
        }
    });

    chatbotToggle.addEventListener('mouseleave', function() {
        if (!isOpen) {
            this.style.transform = 'translateY(0) scale(1)';
        }
    });
});
</script>

<!-- Blog section removed -->
