<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Playwright Test Result Viewer - Chrome Extension</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet" />
  <style>
    :root {
      --primary: #6e8efb;
      --secondary: #a777e3;
      --accent: #ffffff;
      --bg: #f4f6f8;
      --text: #333;
      --card-bg: #ffffff;
      --radius: 12px;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', sans-serif;
      background-color: var(--bg);
      color: var(--text);
      line-height: 1.6;
    }

    header {
      background: linear-gradient(135deg, var(--primary), var(--secondary));
      color: var(--accent);
      padding: 4rem 2rem 2.5rem;
      text-align: center;
    }

    header h1 {
      font-size: 2.5rem;
      font-weight: 700;
    }

    header p {
      font-size: 1.1rem;
      margin-top: 0.5rem;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 2rem 1rem;
    }

    .card {
      background-color: var(--card-bg);
      border-radius: var(--radius);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
      padding: 2rem;
      margin-bottom: 2rem;
      transition: transform 0.3s ease;
    }

    .card:hover {
      transform: translateY(-5px);
    }

    h2 {
      color: var(--primary);
      margin-bottom: 1rem;
      font-size: 1.5rem;
    }

    ol {
      padding-left: 1.2rem;
    }

    pre {
      background: #1e1e1e;
      color: #dcdcdc;
      padding: 1rem;
      border-radius: 6px;
      overflow-x: auto;
      margin-top: 1rem;
      font-size: 0.95rem;
    }

    a.button {
      display: inline-block;
      background: var(--primary);
      color: #fff;
      padding: 0.75rem 1.5rem;
      border-radius: var(--radius);
      text-decoration: none;
      font-weight: 600;
      margin-top: 1rem;
      transition: background 0.3s ease;
    }

    a.button:hover {
      background: #5a76d4;
    }

    footer {
      text-align: center;
      padding: 2rem 1rem;
      font-size: 0.85rem;
      color: #777;
    }

    .icon-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .icon-title img {
      width: 24px;
      height: 24px;
    }
  </style>
</head>
<body>

  <header>
    <h1>Playwright Test Result Viewer</h1>
    <p>A Chrome Extension to Instantly View Your Test Results from GitHub</p>
  </header>

  <main class="container">
    <section class="card">
      <div class="icon-title">
        <img src="https://cdn-icons-png.flaticon.com/512/1828/1828884.png" alt="Lightning icon" />
        <h2>What This Extension Does</h2>
      </div>
      <p>This extension adds a visual badge to your Chrome toolbar showing test pass/fail counts based on a JSON file from your GitHub repo. It’s a quick and clean way to keep an eye on your CI/CD pipelines — especially useful for QA engineers, SDETs, and developers using Playwright.</p>
    </section>

    <section class="card">
      <div class="icon-title">
        <img src="https://cdn-icons-png.flaticon.com/512/929/929430.png" alt="Rocket icon" />
        <h2>How to Set It Up</h2>
      </div>
      <ol>
        <li>Update your <code>playwright.config.ts</code> like this:</li>
        <pre><code>reporter: [
  ['html'],
  ['json', { outputFile: 'tests/test-results.json' }]
]</code></pre>

        <li>Create a GitHub Action to automatically push your test results to a public branch (e.g., <code>gh-pages</code>):</li>
        <pre><code>.github/workflows/publish-results.yml</code></pre>
        <pre><code>name: Publish Playwright Results

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v3

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install dependencies
        run: npm ci

      - name: Run Playwright tests
        run: npx playwright test

      - name: Push test-results.json to gh-pages
        run: |
          mkdir -p output
          cp tests/test-results.json output/test-results.json
          cd output
          git init
          git config user.name "github-actions"
          git config user.email "<EMAIL>"
          git add .
          git commit -m "Update test results"
          git push --force "https://x-access-token:${{ secrets.GITHUB_TOKEN }}@github.com/${{ github.repository }}.git" HEAD:gh-pages
</code></pre>

        <li>In your Chrome extension popup, paste the raw GitHub URL:</li>
        <pre><code>https://raw.githubusercontent.com/&lt;user&gt;/&lt;repo&gt;/gh-pages/test-results.json</code></pre>
        <li>Click <strong>Save</strong> and you're done!</li>
      </ol>
    </section>

    <section class="card">
      <div class="icon-title">
        <img src="https://cdn-icons-png.flaticon.com/512/709/709790.png" alt="Demo icon" />
        <h2>Try It with a Demo</h2>
      </div>
      <p>Want to see it in action? Try it with our sample repo:</p>
      <a class="button" href="https://github.com/faruklmu17/browser_extension_test" target="_blank">View Demo Repository</a>
    </section>

    <section class="card">
      <div class="icon-title">
        <img src="https://cdn-icons-png.flaticon.com/512/747/747376.png" alt="Shield icon" />
        <h2>Privacy & Licensing</h2>
      </div>
      <p>No tracking. No hidden access to your browser history. 100% open source and MIT licensed.</p>
      <a class="button" href="https://github.com/faruklmu17/playwright-test-viewer-extension" target="_blank">Explore on GitHub</a>
    </section>
  </main>

  <footer>
    &copy; 2024 Faruk Hasan | Designed for Playwright automation engineers & developers
  </footer>

</body>
</html>
