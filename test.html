
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Tutoring - <PERSON><PERSON> | STEM Education & Coding for Kids</title>
  <meta name="description" content="Professional STEM tutoring and coding education for ages 9-18. Python, Web Development, AI/ML, Arduino, and Computer Fundamentals with experienced educator <PERSON><PERSON>.">
  <meta name="keywords" content="coding tutoring, STEM education, Python for kids, web development tutoring, AI education, Arduino tutoring, programming lessons">

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"/>
  <link rel="stylesheet" href="styles.css">

  <style>
    :root{
      --bg:#0b1220; --surface:#0f172a; --muted:#0b1220;
      --text:#e5e7eb; --sub:#9ca3af;
      --primary:#7c8cff; --primary-600:#6366f1; --primary-700:#4f46e5;
      --accent:#f59e0b; --ok:#22c55e; --pink:#ec4899; --cyan:#06b6d4;
      --card: rgba(255,255,255,0.06);
      --glass: rgba(255,255,255,0.08);
      --shadow: 0 10px 30px rgba(0,0,0,.25);
      --radius: 16px;
      --grad-1: radial-gradient(1200px 600px at 10% -10%, rgba(99,102,241,.25), transparent 60%),
                radial-gradient(800px 600px at 90% 10%, rgba(236,72,153,.18), transparent 60%),
                radial-gradient(800px 600px at 50% 110%, rgba(14,165,233,.2), transparent 60%);
    }
    *{box-sizing:border-box;margin:0;padding:0}
    html,body{scroll-behavior:smooth}
    body{
      background: var(--bg);
      color: var(--text);
      font-family: Inter, ui-sans-serif, system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial;
      line-height:1.7;
    }
    .section{padding:4.5rem 0}
    .section--tight{padding:2.25rem 0}
    .container{max-width:1200px;margin:0 auto;padding:0 1.25rem}

    /* ====== HERO ====== */
    .hero{
      position:relative;
      background: var(--grad-1);
      overflow:hidden;
      padding:6rem 0 5rem;
      isolation:isolate;
    }
    .hero::after{
      content:"";position:absolute;inset:0;
      background:
        radial-gradient(400px 200px at 80% 20%, rgba(245,158,11,.12), transparent 60%),
        radial-gradient(600px 260px at 0% 100%, rgba(99,102,241,.20), transparent 60%);
      z-index:-1;
    }
    .hero__grid{
      display:grid;gap:2.5rem;align-items:center;
      grid-template-columns: 1.2fr .8fr;
    }
    .hero h1{
      font-size: clamp(2.1rem, 3vw + 1.2rem, 3.2rem);
      line-height:1.15;font-weight:800;letter-spacing:-.02em;
    }
    .hero .lead{
      margin-top:1rem;font-size:1.15rem;color:var(--sub);max-width:48ch;
    }
    .cta-row{display:flex;gap:.85rem;flex-wrap:wrap;margin-top:1.5rem}
    .btn{
      display:inline-flex;align-items:center;gap:.6rem;
      padding:.9rem 1.25rem;border-radius:999px;text-decoration:none;font-weight:700;
      border:1px solid transparent;transition:.25s;will-change:transform,box-shadow;
    }
    .btn--primary{background:linear-gradient(90deg,var(--primary-600),var(--primary-700));color:#fff;box-shadow: var(--shadow)}
    .btn--primary:hover{transform:translateY(-2px)}
    .btn--ghost{background:transparent;color:#fff;border-color:#ffffff33}
    .btn--ghost:hover{background:#ffffff12;border-color:#ffffff66}
    .hero__media{
      display:grid;place-items:center;position:relative;
    }
    .avatar-blob{
      width:min(320px,70vw);aspect-ratio:1/1;border-radius:35% 65% 55% 45%/55% 35% 65% 45%;
      background: conic-gradient(from 180deg at 50% 50%, #1f2937, #0b1220 35%, #1f2937 60%, #0b1220);
      border:1px solid #ffffff14;
      box-shadow: inset 0 0 60px #0008, var(--shadow);
      position:relative;animation:blob 12s ease-in-out infinite;
    }
    .avatar-blob::after{
      content:"👨‍💻";font-size:4rem;position:absolute;inset:0;display:grid;place-items:center;filter:drop-shadow(0 6px 20px #0008)
    }
    @keyframes blob{
      0%,100%{border-radius:35% 65% 55% 45%/55% 35% 65% 45%}
      50%{border-radius:55% 45% 35% 65%/45% 65% 35% 55%}
    }

    /* ====== SECTION TITLES ====== */
    h2{
      font-size: clamp(1.6rem, 1.2rem + 1.8vw, 2.4rem);
      font-weight:800;letter-spacing:-.02em;text-align:center;margin-bottom:.6rem;
    }
    .sub{color:var(--sub);text-align:center;max-width:62ch;margin:.25rem auto 1.75rem}

    /* ====== ABOUT ====== */
    .about{
      display:grid;gap:2rem;grid-template-columns:1.1fr .9fr;
    }
    .about p{color:#b9c0cc}
    .stats{
      display:grid;grid-template-columns:repeat(2,minmax(0,1fr));gap:1rem;margin-top:1rem
    }
    .stat{
      background:var(--glass);border:1px solid #ffffff12;border-radius:var(--radius);
      padding:1.25rem;text-align:center;box-shadow:var(--shadow)
    }
    .stat b{display:block;font-size:2rem}
    .highlight-list{list-style:none;display:grid;gap:.75rem}
    .highlight{
      background: var(--glass);border:1px solid #ffffff12;border-radius:12px;padding:1rem;
      display:flex;gap:.75rem;align-items:flex-start
    }
    .highlight i{color:var(--accent)}

    /* ====== SUBJECTS ====== */
    .grid{
      display:grid;gap:1.25rem;grid-template-columns:repeat(auto-fit,minmax(240px,1fr))
    }
    .card{
      position:relative;border-radius:var(--radius);padding:1.25rem;background:var(--card);
      border:1px solid #ffffff12;box-shadow:var(--shadow);transition:.25s
    }
    .card:hover{transform:translateY(-4px);border-color:#ffffff22}
    .card .icon{
      width:44px;height:44px;border-radius:12px;display:grid;place-items:center;margin-bottom:.6rem;
      background:linear-gradient(120deg, #4755ff33, #06b6d433)
    }
    .pill-row{display:flex;flex-wrap:wrap;gap:.6rem;justify-content:center;margin-top:1rem}
    .pill{padding:.45rem .8rem;border-radius:999px;background:#ffffff10;border:1px solid #ffffff1a;color:#e5e7eb;font-weight:600;font-size:.9rem}

    /* ====== EXPERIENCE / TIMELINE ====== */
    .timeline{position:relative;margin:2rem auto 0;max-width:800px}
    .timeline::before{content:"";position:absolute;left:22px;top:0;bottom:0;width:2px;background:#ffffff1f}
    .t-item{position:relative;padding-left:70px;margin-bottom:1.25rem}
    .t-dot{
      position:absolute;left:10px;top:.2rem;width:24px;height:24px;border-radius:50%;
      background:linear-gradient(135deg,var(--primary-600),var(--pink));border:2px solid #0b1220;box-shadow:0 0 0 6px #0b1220
    }
    .t-card{background:var(--card);border:1px solid #ffffff12;border-radius:14px;padding:1rem}
    .t-card h3{margin:0 0 .35rem;font-size:1.05rem}

    /* ====== PLATFORMS ====== */
    .platforms .p-card{
      text-decoration:none;color:inherit
    }
    .p-card .icon{background:linear-gradient(120deg,#4755ff33,#ec489933)}

    /* ====== COURSES (compact, image + badges) ====== */
    .courses{display:grid;gap:1.25rem;grid-template-columns:repeat(auto-fit,minmax(280px,1fr))}
    .course{
      display:flex;flex-direction:column;gap:.75rem;background:var(--card);
      border:1px solid #ffffff12;border-radius:16px;overflow:hidden;box-shadow:var(--shadow)
    }
    .course .img{height:140px;overflow:hidden}
    .course .img img{width:100%;height:100%;object-fit:cover;transform:scale(1.02);transition:.3s}
    .course:hover .img img{transform:scale(1.06)}
    .badge{position:absolute;top:10px;right:12px;background:linear-gradient(90deg,var(--primary-600),var(--primary-700));color:#fff;padding:.25rem .6rem;border-radius:999px;font-size:.75rem;font-weight:700}
    .meta{display:flex;gap:.5rem;align-items:center;color:#9aa3b2;font-size:.85rem}
    .c-body{padding:1rem}
    .c-title{font-weight:800}
    .c-btn{align-self:flex-start;margin:0 1rem 1rem;padding:.45rem .8rem;border-radius:10px;text-decoration:none;background:#ffffff12;border:1px solid #ffffff2a;color:#fff;font-weight:700}
    .c-btn:hover{background:#ffffff1f}

    /* ====== REVIEWS (scroll-snap) ====== */
    .reviews{
      display:flex;gap:1rem;overflow:auto;padding:.5rem;scroll-snap-type:x mandatory
    }
    .reviews::-webkit-scrollbar{height:8px}
    .reviews::-webkit-scrollbar-thumb{background:#ffffff1a;border-radius:999px}
    .quote{
      scroll-snap-align:start;min-width:280px;background:var(--card);border:1px solid #ffffff12;border-radius:14px;padding:1rem
    }
    .stars{color:#fbbf24}

    /* ====== CONTACT ====== */
    .contact{
      position:relative;background:
        radial-gradient(900px 400px at 10% 0%, rgba(124,140,255,.12), transparent 60%),
        radial-gradient(900px 400px at 100% 90%, rgba(245,158,11,.12), transparent 60%);
      border-top:1px solid #ffffff12;
    }
    .form{
      max-width:680px;margin:1.25rem auto 0;background:#0b1220aa;border:1px solid #ffffff14;
      padding:1.25rem;border-radius:16px;backdrop-filter: blur(6px)
    }
    .row{display:grid;gap:.75rem;grid-template-columns:1fr 1fr}
    .row.full{grid-template-columns:1fr}
    label{font-size:.9rem;color:#cbd5e1}
    input,textarea{
      width:100%;margin-top:.35rem;padding:.8rem .9rem;border-radius:12px;border:1px solid #ffffff1f;background:#0b1220;color:#e5e7eb
    }
    input:focus,textarea:focus{outline:none;border-color:#7c8cff33;box-shadow:0 0 0 4px #7c8cff17}
    .actions{display:flex;gap:.75rem;justify-content:center;margin-top:.75rem}

    /* ====== UTIL ====== */
    .muted{color:var(--sub)}
    .sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}

    /* ====== RESPONSIVE ====== */
    @media (max-width: 980px){
      .hero__grid{grid-template-columns:1fr}
      .about{grid-template-columns:1fr}
      .row{grid-template-columns:1fr}
    }
  </style>
</head>
<body>

  <!-- ===== KEEPING YOUR EXISTING HEADER UNCHANGED ===== -->
  <header>
    <div class="header-content">
      <div class="header-left">
        <div class="profile-image">
          <img src="me.jpg" alt="Faruk Hasan">
        </div>
        <div class="headline">
          <h1>Faruk Hasan</h1>
          <p><span class="highlight">Software QA Engineer | Automation & AI-Driven Testing Specialist</span></p>
        </div>
      </div>
      <div class="nav-social-container">
        <nav>
          <ul>
            <li><a href="index.html#about">About Me</a></li>
            <li><a href="index.html#courses">Courses</a></li>
            <li><a href="index.html#projects">Projects</a></li>
            <li><a href="index.html#blog">Blog</a></li>
            <li><a href="index.html#my dividends">Dividends</a></li>
            <li><a href="resources.html">Resources</a></li>
          </ul>
        </nav>
        <div class="social-icons">
          <a href="https://www.linkedin.com/in/md-faruk-hasan/" class="social-icon linkedin" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
          <a href="https://www.youtube.com/@kidz_code" class="social-icon youtube" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
          <a href="https://www.facebook.com/HasanMd2020/" class="social-icon facebook" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
        </div>
      </div>
    </div>
  </header>

  <!-- ===== MODERN TUTORING PAGE ===== -->

  <!-- HERO -->
  <section id="hero" class="section hero" aria-labelledby="hero-title">
    <div class="container hero__grid">
      <div>
        <h1 id="hero-title">Inspiring the Next Generation of Coders & Thinkers</h1>
        <p class="lead">
          Hi, I'm <strong>Faruk Hasan</strong> — STEM Educator & Senior QA Engineer.
          I teach Python, Web Dev, AI/ML, Arduino, and Computer Fundamentals for ages 9–18.
        </p>
        <div class="cta-row">
          <a class="btn btn--primary" href="#subjects"><i class="fa-solid fa-rocket"></i> View Courses</a>
          <a class="btn btn--ghost" href="#contact"><i class="fa-solid fa-calendar-check"></i> Book Free Consult</a>
        </div>
      </div>
      <div class="hero__media">
        <div class="avatar-blob" aria-hidden="true"></div>
      </div>
    </div>
  </section>

  <!-- ABOUT -->
  <section id="about" class="section">
    <div class="container about">
      <div>
        <h2>Why Learn With Me?</h2>
        <p class="sub">Project-based lessons, industry best practices, and kid-friendly pacing that builds confidence.</p>
        <p>
          I blend my day-to-day experience as an SDET (Playwright, Selenium, APIs, CI/CD, AI-assisted testing) with
          engaging activities and real-world mini-projects. Learners leave with portfolio-ready work and practical skills.
        </p>
        <div class="stats" role="list" aria-label="Quick stats">
          <div class="stat" role="listitem"><b>10+</b><span class="muted">Years Teaching</span></div>
          <div class="stat" role="listitem"><b>500+</b><span class="muted">Students Taught</span></div>
        </div>
      </div>
      <ul class="highlight-list" aria-label="Highlights">
        <li class="highlight"><i class="fa-solid fa-person-chalkboard"></i><span>Live, interactive classes with clear outcomes</span></li>
        <li class="highlight"><i class="fa-solid fa-bug-slash"></i><span>Debugging mindset, clean code, and testing</span></li>
        <li class="highlight"><i class="fa-solid fa-shield-halved"></i><span>Kid-safe, parent-friendly communication</span></li>
        <li class="highlight"><i class="fa-solid fa-diagram-project"></i><span>Portfolio projects for school and internships</span></li>
      </ul>
    </div>
  </section>

  <!-- SUBJECTS -->
  <section id="subjects" class="section section--tight">
    <div class="container">
      <h2>What I Teach</h2>
      <p class="sub">Practical, engaging STEM topics tailored for ages 9–18 — from coding fundamentals to AI and hardware.</p>

      <div class="grid" role="list">
        <article class="card" role="listitem">
          <div class="icon"><i class="fa-brands fa-python"></i></div>
          <h3>Python Programming</h3>
          <p>From fundamentals to mini-apps: loops, functions, data, OOP, and beginner ML.</p>
        </article>

        <article class="card" role="listitem">
          <div class="icon"><i class="fa-solid fa-globe"></i></div>
          <h3>Web Development</h3>
          <p>HTML, CSS, JS. Build responsive sites, interactive apps, and deploy to GitHub Pages.</p>
        </article>

        <article class="card" role="listitem">
          <div class="icon"><i class="fa-solid fa-robot"></i></div>
          <h3>AI & Machine Learning</h3>
          <p>Kid-friendly AI: BoW, Naive Bayes, simple chatbots, ethics & safety.</p>
        </article>

        <article class="card" role="listitem">
          <div class="icon"><i class="fa-solid fa-microchip"></i></div>
          <h3>Arduino & Electronics</h3>
          <p>Hands-on circuits, sensors, LEDs, and simple automation (with Wokwi sims).</p>
        </article>

        <article class="card" role="listitem">
          <div class="icon"><i class="fa-solid fa-vial"></i></div>
          <h3>QA Testing (Beginner)</h3>
          <p>Testing mindset, Playwright/Selenium basics, and intro to automation.</p>
        </article>

        <article class="card" role="listitem">
          <div class="icon"><i class="fa-solid fa-computer"></i></div>
          <h3>Computer Fundamentals</h3>
          <p>OS basics, files, browsers & DevTools, safe internet habits, CLI intro.</p>
        </article>
      </div>

      <div class="pill-row" aria-label="Who I teach">
        <span class="pill">Grades 4–5</span>
        <span class="pill">Grades 6–8</span>
        <span class="pill">Grades 9–12</span>
        <span class="pill">1:1 & Small Groups</span>
        <span class="pill">Project-Based</span>
      </div>
    </div>
  </section>

  <!-- EXPERIENCE -->
  <section id="experience" class="section">
    <div class="container">
      <h2>Professional Experience</h2>
      <p class="sub">Real industry skills brought to age-appropriate, engaging lessons.</p>
      <div class="timeline" role="list">
        <div class="t-item" role="listitem">
          <div class="t-dot" aria-hidden="true"></div>
          <div class="t-card">
            <h3><i class="fa-solid fa-code"></i> Senior QA Engineer (SDET)</h3>
            <p class="muted">Playwright, Selenium, API testing, CI/CD, Python frameworks, AI-assisted testing.</p>
          </div>
        </div>
        <div class="t-item" role="listitem">
          <div class="t-dot" aria-hidden="true"></div>
          <div class="t-card">
            <h3><i class="fa-solid fa-chalkboard-user"></i> STEM Educator & Curriculum Designer</h3>
            <p class="muted">Project-based courses in Python, Web, AI/ML, and Arduino with portfolio outcomes.</p>
          </div>
        </div>
        <div class="t-item" role="listitem">
          <div class="t-dot" aria-hidden="true"></div>
          <div class="t-card">
            <h3><i class="fa-solid fa-graduation-cap"></i> Mentor to 500+ Students</h3>
            <p class="muted">Guided learners from “Hello, World!” to publishing apps and showcasing projects.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- PLATFORMS -->
  <section id="platforms" class="section section--tight">
    <div class="container">
      <h2>Find Me Online</h2>
      <p class="sub">Book a session, watch tutorials, and connect professionally.</p>
      <div class="grid platforms">
        <a class="card p-card" href="https://outschool.com/classes/private-coding-tutoring-python-java-c-ai-web-dev-git-cloud-computing-e7JekWpy" target="_blank" rel="noopener">
          <div class="icon"><i class="fa-solid fa-calendar-check"></i></div>
          <h3>Outschool</h3>
          <p class="muted">1:1 and small-group tutoring. Flexible scheduling.</p>
        </a>
        <a class="card p-card" href="https://www.youtube.com/@kidz_code" target="_blank" rel="noopener">
          <div class="icon"><i class="fab fa-youtube"></i></div>
          <h3>YouTube</h3>
          <p class="muted">Kid-friendly coding tutorials and demos.</p>
        </a>
        <a class="card p-card" href="https://www.linkedin.com/in/md-faruk-hasan/" target="_blank" rel="noopener">
          <div class="icon"><i class="fab fa-linkedin-in"></i></div>
          <h3>LinkedIn</h3>
          <p class="muted">Experience, skills, and recommendations.</p>
        </a>
        <a class="card p-card" href="https://github.com/faruklmu17" target="_blank" rel="noopener">
          <div class="icon"><i class="fab fa-github"></i></div>
          <h3>GitHub</h3>
          <p class="muted">Projects, examples, and course code.</p>
        </a>
      </div>
    </div>
  </section>

  <!-- COURSES -->
  <section id="courses" class="section section--tight">
    <div class="container">
      <h2>Popular Courses</h2>
      <p class="sub">Each course ships with projects and a portfolio piece.</p>

      <div class="courses">
        <article class="course">
          <div class="img" style="position:relative">
            <img src="images/course-python.jpg" alt="Python course banner">
            <span class="badge">Beginner</span>
          </div>
          <div class="c-body">
            <div class="meta"><i class="fa-regular fa-clock"></i><span>10–14 yrs</span></div>
            <h3 class="c-title">Python Fundamentals for Kids</h3>
            <p class="muted">Variables to loops, lists, functions, and mini-projects that build confidence.</p>
          </div>
          <a class="c-btn" href="#contact"><i class="fa-solid fa-paper-plane"></i> Enroll / Ask</a>
        </article>

        <article class="course">
          <div class="img" style="position:relative">
            <img src="images/course-web.jpg" alt="Web development course banner">
            <span class="badge" style="background:linear-gradient(90deg,#06b6d4,#3b82f6)">Project-Based</span>
          </div>
          <div class="c-body">
            <div class="meta"><i class="fa-regular fa-clock"></i><span>11–16 yrs</span></div>
            <h3 class="c-title">Web Dev: HTML, CSS & JavaScript</h3>
            <p class="muted">Build and deploy a personal website and interactive apps using modern tools.</p>
          </div>
          <a class="c-btn" href="#contact"><i class="fa-solid fa-paper-plane"></i> Enroll / Ask</a>
        </article>

        <article class="course">
          <div class="img" style="position:relative">
            <img src="images/course-ai.jpg" alt="AI for beginners course banner">
            <span class="badge" style="background:linear-gradient(90deg,#f59e0b,#ef4444)">STEM+</span>
          </div>
          <div class="c-body">
            <div class="meta"><i class="fa-regular fa-clock"></i><span>12–18 yrs</span></div>
            <h3 class="c-title">AI for Beginners (Kid-Friendly)</h3>
            <p class="muted">Understand data basics, train a simple classifier, and build a safe chatbot.</p>
          </div>
          <a class="c-btn" href="#contact"><i class="fa-solid fa-paper-plane"></i> Enroll / Ask</a>
        </article>
      </div>
    </div>
  </section>

  <!-- REVIEWS -->
  <section id="reviews" class="section">
    <div class="container">
      <h2>Parent & Student Reviews</h2>
      <div class="reviews" tabindex="0" aria-label="Reviews carousel">
        <figure class="quote">
          <div class="stars" aria-label="5 stars">★★★★★</div>
          <blockquote>“Faruk keeps lessons fun and clear. My son looks forward to class every week and proudly shows his projects.”</blockquote>
          <figcaption class="muted">— Daniela, Parent</figcaption>
        </figure>
        <figure class="quote">
          <div class="stars" aria-label="5 stars">★★★★★</div>
          <blockquote>“The AI course made complex ideas accessible. Loved the mini-projects and the supportive teaching style.”</blockquote>
          <figcaption class="muted">— Alex, Student (14)</figcaption>
        </figure>
        <figure class="quote">
          <div class="stars" aria-label="5 stars">★★★★★</div>
          <blockquote>“Great communication and pacing. We appreciate the emphasis on problem-solving and best practices.”</blockquote>
          <figcaption class="muted">— Priya, Parent</figcaption>
        </figure>
      </div>
    </div>
  </section>

  <!-- CONTACT -->
  <section id="contact" class="section contact" aria-labelledby="contact-title">
    <div class="container">
      <h2 id="contact-title">Book a Free Consultation</h2>
      <p class="sub">Tell me a bit about your learner and goals. I’ll recommend the best path and course fit.</p>

      <form class="form" id="contactForm">
        <div class="row">
          <div>
            <label for="userName">Your Name</label>
            <input id="userName" name="name" type="text" placeholder="Parent/Guardian name" required />
          </div>
          <div>
            <label for="userEmail">Email</label>
            <input id="userEmail" name="email" type="email" placeholder="<EMAIL>" required />
          </div>
        </div>
        <div class="row">
          <div>
            <label for="learnerName">Learner's Name</label>
            <input id="learnerName" name="learner" type="text" placeholder="Student name"/>
          </div>
          <div>
            <label for="learnerAge">Learner's Age</label>
            <input id="learnerAge" name="age" type="number" min="5" max="19" placeholder="e.g., 12"/>
          </div>
        </div>
        <div class="row full">
          <div>
            <label for="userMessage">Goals / Notes</label>
            <textarea id="userMessage" name="message" rows="5" placeholder="Experience level, interests (Python/Web/AI/Arduino), preferred schedule..."></textarea>
          </div>
        </div>
        <div class="actions">
          <button type="button" class="btn btn--primary" id="sendEmailBtn"><i class="fa-solid fa-paper-plane"></i> Send</button>
          <a class="btn btn--ghost" href="https://outschool.com/classes/private-coding-tutoring-python-java-c-ai-web-dev-git-cloud-computing-e7JekWpy" target="_blank" rel="noopener">
            <i class="fa-solid fa-calendar"></i> Book on Outschool
          </a>
        </div>
      </form>
    </div>
  </section>

  <!-- ===== KEEPING YOUR EXISTING FOOTER UNCHANGED ===== -->
  <footer>
    <div class="footer-content">
      <p>&copy; 2024 Faruk Hasan. All rights reserved.</p>
    </div>
  </footer>

  <script>
    // Smooth scrolling for in-page anchors
    document.addEventListener('click', (e) => {
      const a = e.target.closest('a[href^="#"]');
      if (!a) return;
      const id = a.getAttribute('href');
      if (id.length > 1) {
        e.preventDefault();
        const el = document.querySelector(id);
        if (el) el.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    });

    // Mailto sender (kept from your original approach)
    function sendEmail() {
      const name = document.getElementById('userName').value || 'Anonymous';
      const email = document.getElementById('userEmail').value || 'No email provided';
      const learner = document.getElementById('learnerName').value || '—';
      const age = document.getElementById('learnerAge').value || '—';
      const message = document.getElementById('userMessage').value || 'No message provided';

      const subject = `Tutoring Inquiry from ${name}`;
      const body = `Hi Faruk,

I'm interested in your tutoring services.

Parent/Guardian: ${name}
Email: ${email}
Learner: ${learner}
Age: ${age}

Message:
${message}

Best regards,
${name}`;

      const mailtoUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      try { window.open(mailtoUrl, '_self'); }
      catch { window.location.href = mailtoUrl; }
    }
    document.getElementById('sendEmailBtn')?.addEventListener('click', (e) => { e.preventDefault(); sendEmail(); });
  </script>
</body>
</html>

