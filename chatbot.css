/* chatbot.css */

/* Chatbot Container */
.chatbot-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Minimized Chatbot Button */
.chatbot-toggle {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.chatbot-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.chatbot-toggle:active {
    transform: translateY(0);
}

/* Chat Icon */
.chat-icon {
    width: 28px;
    height: 28px;
    fill: white;
    transition: transform 0.3s ease;
}

.chatbot-toggle:hover .chat-icon {
    transform: scale(1.1);
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 20px;
    height: 20px;
    background: #ff4757;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    color: white;
    border: 2px solid white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Expanded Chatbot Window */
.chatbot-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    transform: scale(0.8) translateY(20px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.chatbot-window.active {
    transform: scale(1) translateY(0);
    opacity: 1;
    visibility: visible;
}

/* Chatbot Header */
.chatbot-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chatbot-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    font-size: 16px;
}

.chatbot-avatar {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.minimize-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.minimize-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.minimize-icon {
    width: 20px;
    height: 20px;
    fill: currentColor;
}

/* Chatbot Frame */
.chatbot-frame {
    width: 100%;
    height: calc(100% - 68px);
    border: none;
    background: #f8f9fa;
}

/* Loading State */
.chatbot-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f8f9fa;
    flex-direction: column;
    gap: 16px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e9ecef;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #6c757d;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chatbot-container {
        bottom: 15px;
        right: 15px;
    }

    .chatbot-window {
        width: 320px;
        height: 450px;
        bottom: 75px;
    }

    .chatbot-toggle {
        width: 55px;
        height: 55px;
    }

    .chat-icon {
        width: 24px;
        height: 24px;
    }
}

@media (max-width: 480px) {
    .chatbot-window {
        width: calc(100vw - 30px);
        right: -15px;
        height: 400px;
    }
}
