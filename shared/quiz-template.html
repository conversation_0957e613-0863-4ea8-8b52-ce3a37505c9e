<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Quiz Template | Your Quiz Title Here</title>
  <meta name="description" content="Your quiz description here" />
  <meta name="keywords" content="quiz, programming, learning" />
  <meta name="author" content="Faruk Hasan" />

  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Shared Header and Footer Styles -->
  <link rel="stylesheet" href="../shared/header-footer.css">

  <style>
    /* Quiz-specific styles go here */
    :root {
      --quiz-primary-color: #3498db; /* Change this for different quiz themes */
      --quiz-secondary-color: #2c3e50;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: var(--quiz-secondary-color);
      line-height: 1.6;
    }

    /* Header margin adjustment for quiz */
    header {
      margin-bottom: 2rem;
    }

    /* Quiz container */
    .quiz-container {
      max-width: 900px;
      margin: 0 auto;
      padding: 0 2rem 2rem;
    }

    .quiz-title {
      text-align: center;
      color: white;
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .quiz-subtitle {
      text-align: center;
      color: rgba(255, 255, 255, 0.9);
      font-size: 1.2rem;
      margin-bottom: 2rem;
      font-weight: 300;
    }

    /* Question styles */
    .question {
      background: white;
      border-radius: 12px;
      padding: 2rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .question:hover {
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .quiz-container {
        padding: 0 1rem 2rem;
      }

      .quiz-title {
        font-size: 2rem;
      }

      .question {
        padding: 1.5rem;
      }
    }
  </style>
</head>
<body>
  <!-- Header -->
  <header>
    <div class="header-content">
      <div class="header-left">
        <div class="profile-image">
          <img src="../me.jpg" alt="Faruk Hasan">
        </div>
        <div class="headline">
          <h1>Faruk Hasan</h1>
          <p><span class="highlight">Software QA Engineer | Automation & AI-Driven Testing Specialist</span></p>
        </div>
      </div>
      <div class="nav-social-container">
        <nav>
          <ul>
            <li><a href="../index.html#about">About Me</a></li>
            <li><a href="../index.html#courses">Courses</a></li>
            <li><a href="../index.html#projects">Projects</a></li>
            <li><a href="../index.html#blog">Blog</a></li>
            <li><a href="../index.html#my dividends">Dividends</a></li>
            <li><a href="../resources.html">Resources</a></li>
          </ul>
        </nav>
        <div class="social-icons">
          <a href="https://www.linkedin.com/in/md-faruk-hasan/" class="social-icon linkedin" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
          <a href="https://www.youtube.com/@kidz_code" class="social-icon youtube" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
          <a href="https://www.facebook.com/HasanMd2020/" class="social-icon facebook" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <div class="quiz-container">
    <h1 class="quiz-title">
      <i class="fas fa-question-circle"></i> Your Quiz Title
    </h1>
    <p class="quiz-subtitle">Your quiz subtitle here</p>

    <!-- Quiz content goes here -->
    <div class="question">
      <p>Sample question content...</p>
    </div>

  </div>

  <!-- Footer -->
  <footer>
    <div class="footer-content">
      <div class="footer-info">
        <div class="footer-logo">Faruk Hasan</div>
        <p>QA Engineer | Automation & AI-Driven Testing Specialist</p>
      </div>

      <div class="footer-social">
        <h4>Connect With Me</h4>
        <div class="social-icons-footer">
          <a href="https://www.linkedin.com/in/md-faruk-hasan/" class="social-icon linkedin" aria-label="LinkedIn" target="_blank"><i class="fab fa-linkedin"></i></a>
          <a href="https://www.youtube.com/@kidz_code" class="social-icon youtube" aria-label="YouTube" target="_blank"><i class="fab fa-youtube"></i></a>
          <a href="https://www.facebook.com/HasanMd2020/" class="social-icon facebook" aria-label="Facebook" target="_blank"><i class="fab fa-facebook-f"></i></a>
          <a href="https://github.com/faruklmu17" class="social-icon github" aria-label="GitHub" target="_blank"><i class="fab fa-github"></i></a>
        </div>
      </div>

      <div class="footer-share">
        <h4>Share This Page</h4>
        <div class="share-buttons">
          <a href="javascript:void(0)" onclick="shareOnFacebook()" class="share-btn facebook" aria-label="Share on Facebook"><i class="fab fa-facebook-f"></i></a>
          <a href="javascript:void(0)" onclick="shareOnTwitter()" class="share-btn twitter" aria-label="Share on Twitter"><i class="fab fa-twitter"></i></a>
          <a href="javascript:void(0)" onclick="shareOnLinkedIn()" class="share-btn linkedin" aria-label="Share on LinkedIn"><i class="fab fa-linkedin-in"></i></a>
          <a href="javascript:void(0)" onclick="shareByEmail()" class="share-btn email" aria-label="Share by Email"><i class="fas fa-envelope"></i></a>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <p>&copy; 2025 Faruk Hasan. All rights reserved.</p>
    </div>
  </footer>

  <!-- Shared Header and Footer JavaScript -->
  <script src="../shared/header-footer.js"></script>
  
  <!-- Quiz-specific JavaScript goes here -->
  <script>
    // Your quiz-specific JavaScript functions
  </script>

</body>
</html>
