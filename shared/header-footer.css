/* Shared <PERSON><PERSON> and <PERSON><PERSON> Styles for All Quizzes */
/* Exact match with resources.html and styles.css */

/* CSS Variables - Matching styles.css */
:root {
  --primary-color: #3a6ea5;
  --secondary-color: #2c3e50;
  --background-color: #f4f7f6;
  --text-color: #333;
  --accent-color: #2980b9;
  --light-background: #ffffff;
  --soft-shadow: 0 10px 25px rgba(0,0,0,0.05);
}

/* CSS Reset - Matching styles.css */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Google Fonts Import */
@import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

/* Body styles - Matching styles.css */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-color);
  margin: 0;
  padding: 0;
}

/* ============================================
   HEADER STYLES - Matching resources.html
   ============================================ */

header {
  background-color: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 15px 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  margin: 0;
  width: 100%;
  box-sizing: border-box;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  flex-direction: column;
  gap: 10px;
  box-sizing: border-box;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* First row: Home button + Name */
.header-row-1 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Second row: Subtitle */
.header-row-2 {
  width: 100%;
  text-align: center;
}

/* Third row: Navigation + Social icons */
.header-row-3 {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.logo-container {
  /* Home button container */
}

.home-button {
  display: flex;
  align-items: center;
  background-color: #0077b5;
  color: white;
  padding: 8px 15px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 14px;
}

.home-button:hover {
  background-color: #005d8f;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.home-button i {
  margin-right: 5px;
  font-size: 16px;
}

.headline {
  text-align: center;
}

.headline h1 {
  margin: 0;
  font-size: 28px;
  color: #333;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-weight: 600;
}

.header-row-2 p {
  margin: 0;
  color: #555;
  font-size: 14px;
  font-weight: 500;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

nav {
  display: flex;
  justify-content: center;
}

nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 12px;
  flex-wrap: nowrap;
}

nav a {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  padding: 5px 8px;
  position: relative;
  transition: color 0.3s;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 13px;
  white-space: nowrap;
}

nav a:hover, nav a.active {
  color: #0077b5;
}

nav a.active:after, nav a:hover:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #0077b5;
}

.social-icons {
  display: flex;
  gap: 12px;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: white;
  color: #333;
  transition: transform 0.3s, box-shadow 0.3s;
  border: 1px solid #eaeaea;
  text-decoration: none;
}

.social-icon:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.social-icon.linkedin {
  color: #0077b5;
}

.social-icon.linkedin:hover {
  background-color: #0077b5;
  color: white;
}

.social-icon.youtube {
  color: #ff0000;
}

.social-icon.youtube:hover {
  background-color: #ff0000;
  color: white;
}

.social-icon.facebook {
  color: #1877f2;
}

.social-icon.facebook:hover {
  background-color: #1877f2;
  color: white;
}

/* ============================================
   FOOTER STYLES - Matching styles.css
   ============================================ */

footer {
  background-color: #2c3e50;
  color: white;
  padding: 0;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  padding: 3rem 2rem;
}

.footer-info {
  display: flex;
  flex-direction: column;
}

.footer-logo {
  font-family: 'Dancing Script', cursive;
  font-size: 2rem;
  margin-bottom: 1rem;
  color: white;
}

.footer-info p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.footer-social h4, .footer-share h4 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.social-icons-footer {
  display: flex;
  gap: 1rem;
}

.social-icons-footer .social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 50%;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-icons-footer .social-icon:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

.social-icons-footer .linkedin:hover {
  background-color: #0077b5;
}

.social-icons-footer .youtube:hover {
  background-color: #ff0000;
}

.social-icons-footer .facebook:hover {
  background-color: #1877f2;
}

.social-icons-footer .github:hover {
  background-color: #333;
}

.share-buttons {
  display: flex;
  gap: 1rem;
}

.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 50%;
  transition: all 0.3s ease;
  text-decoration: none;
  cursor: pointer;
}

.share-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

.share-btn.facebook:hover {
  background-color: #1877f2;
}

.share-btn.twitter:hover {
  background-color: #1da1f2;
}

.share-btn.linkedin:hover {
  background-color: #0077b5;
}

.share-btn.email:hover {
  background-color: #ea4335;
}

.footer-bottom {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 1rem;
  text-align: center;
}

.footer-bottom p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.footer-bottom a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
}

.footer-bottom a:hover {
  color: white;
}

/* ============================================
   RESPONSIVE STYLES - Matching resources.html
   ============================================ */

/* Responsive Styles */
@media (max-width: 768px) {
  /* Stack everything vertically on mobile */
  .header-row-1 {
    flex-direction: column;
    gap: 10px;
  }

  .header-row-3 {
    flex-direction: column;
    gap: 15px;
  }

  nav ul {
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
  }

  /* Footer responsive */
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .social-icons-footer, .share-buttons {
    justify-content: center;
  }
}
