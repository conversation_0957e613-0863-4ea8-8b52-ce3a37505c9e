
# Playwright
node_modules/
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/
/playwright/.auth/

# Additional Playwright files
*.trace
*.zip
/coverage/
/allure-results/
/allure-report/
/screenshots/
/videos/
/downloads/
playwright-report.html
test-results.json
.env.local
.env.test

# Package manager files and directories
# npm
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# pnpm
.pnpm-debug.log*
.pnpm-store/

# Yarn v2+
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*


